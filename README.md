# Almamry ERP - نظام إدارة موارد المؤسسات

نظام ERP كامل ومتطور لشركة المامري للزجاج والألمنيوم، يعمل بشكل كامل بدون إنترنت.

## 🚀 المميزات

### 📊 الوحدات المتوفرة
- **لوحة التحكم**: إحصائيات شاملة وKPIs
- **المبيعات**: إدارة العملاء وفواتير المبيعات
- **المشتريات**: إدارة الموردين وفواتير المشتريات
- **المخزون**: إدارة الأصناف وحركة المخزون
- **المحاسبة**: دليل الحسابات ودفتر اليومية
- **الموارد البشرية**: إدارة الموظفين والرواتب
- **التصنيع**: أوامر التصنيع وإدارة الإنتاج
- **المصروفات**: تتبع المصروفات والإيرادات

### 💾 قاعدة البيانات
- **SQLite**: قاعدة بيانات محلية قوية وموثوقة
- **Prisma ORM**: إدارة متقدمة لقاعدة البيانات
- **نسخ احتياطي**: إمكانية النسخ الاحتياطي والاستعادة
- **تشفير**: حماية البيانات الحساسة

### 🖥️ الواجهة
- **Electron**: تطبيق سطح المكتب
- **React**: واجهة مستخدم حديثة وسريعة
- **Tailwind CSS**: تصميم جميل ومتجاوب
- **Chart.js**: رسوم بيانية تفاعلية

## 🛠️ المتطلبات

- Node.js 18+ 
- npm أو yarn

## 📦 التثبيت

```bash
# استنساخ المشروع
git clone [repository-url]
cd AlmamryERP

# تثبيت التبعيات
npm install

# إنشاء قاعدة البيانات
npx prisma db push

# تشغيل النظام
npm start
```

## 🚀 التشغيل

### التطوير
```bash
npm start
```
هذا الأمر سيشغل:
- Vite dev server على المنفذ 5173
- Electron app متصل بـ Vite

### الإنتاج
```bash
npm run build
npm run dist
```

## 🔐 تسجيل الدخول

**بيانات المستخدم الافتراضية:**
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`

## 📁 هيكل المشروع

```
AlmamryERP/
├── electron/                 # ملفات Electron
│   ├── main.js              # النافذة الرئيسية
│   └── preload.js           # واجهة API آمنة
├── prisma/                  # قاعدة البيانات
│   ├── schema.prisma        # مخطط قاعدة البيانات
│   └── migrations/          # ملفات الهجرة
├── src/                     # كود React
│   ├── components/          # المكونات
│   ├── pages/              # صفحات النظام
│   ├── services/           # خدمات API
│   ├── stores/             # إدارة الحالة (Zustand)
│   └── types/              # أنواع TypeScript
└── package.json
```

## 🗄️ قاعدة البيانات

### الجداول الرئيسية
- **SystemSettings**: إعدادات النظام
- **User**: المستخدمين والصلاحيات
- **Customer**: العملاء
- **Supplier**: الموردين
- **Item**: الأصناف والمنتجات
- **SalesInvoice**: فواتير المبيعات
- **PurchaseInvoice**: فواتير المشتريات
- **Account**: دليل الحسابات
- **JournalEntry**: دفتر اليومية
- **Employee**: الموظفين
- **PayrollRecord**: سجلات الرواتب
- **ManufacturingOrder**: أوامر التصنيع
- **Expense**: المصروفات

### العلاقات
- علاقات متقدمة بين جميع الجداول
- دعم التسلسل الهرمي للحسابات
- تتبع حركة المخزون
- ربط الفواتير بالمدفوعات

## 🔧 الإعدادات

### إعدادات النظام الافتراضية
- **اسم الشركة**: شركة المامري للزجاج والألمنيوم
- **العملة**: دينار ليبي (ل.د)
- **العنوان**: طرابلس، ليبيا

### تخصيص الإعدادات
يمكن تعديل إعدادات النظام من خلال:
1. واجهة المستخدم في النظام
2. تعديل ملف `prisma/schema.prisma`
3. استخدام Prisma Studio: `npx prisma studio`

## 📊 الإحصائيات

النظام يوفر إحصائيات شاملة:
- إجمالي المبيعات والمشتريات
- عدد العملاء والموردين
- حركة المخزون
- الأداء المالي
- تحليلات متقدمة

## 🔒 الأمان

- **Context Isolation**: حماية بين العمليات
- **Preload Scripts**: واجهة API آمنة
- **Input Validation**: التحقق من المدخلات
- **Error Handling**: معالجة شاملة للأخطاء

## 🚀 التطوير

### إضافة ميزات جديدة
1. تحديث مخطط قاعدة البيانات في `prisma/schema.prisma`
2. إنشاء المكونات في `src/components/`
3. إضافة الصفحات في `src/pages/`
4. تحديث الخدمات في `src/services/`

### قاعدة البيانات
```bash
# عرض قاعدة البيانات
npx prisma studio

# إعادة إنشاء قاعدة البيانات
npx prisma db push

# إنشاء هجرة جديدة
npx prisma migrate dev --name [migration-name]
```

## 📞 الدعم

للمساعدة والدعم التقني:
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +218-21-1234567

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT.

---

**تم تطوير هذا النظام خصيصاً لشركة المامري للزجاج والألمنيوم** 🏢 