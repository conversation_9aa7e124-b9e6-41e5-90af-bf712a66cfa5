{"name": "almamry-erp", "version": "1.0.0", "description": "نظام إدارة موارد المؤسسات الليبي - Almamry ERP System", "main": "electron/main.js", "homepage": "./", "scripts": {"start": "concurrently \"npm run dev\" \"npm run electron\"", "dev": "vite", "build": "tsc && vite build", "electron": "wait-on http://localhost:5173 && electron .", "electron-dev": "electron .", "dist": "npm run build && electron-builder", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:init": "node scripts/init-database.js", "db:reset": "reset-database.bat"}, "prisma": {"seed": "node prisma/seed.js"}, "keywords": ["erp", "accounting", "inventory", "libya", "<PERSON><PERSON><PERSON>"], "author": "Almamry ERP Team", "license": "MIT", "devDependencies": {"@types/node": "^20.19.2", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@vitejs/plugin-react": "^4.2.1", "concurrently": "^8.2.2", "electron": "^28.0.0", "electron-builder": "^24.13.3", "prisma": "^5.7.1", "ts-node": "^10.9.2", "typescript": "^5.8.3", "vite": "^5.0.10", "wait-on": "^7.2.0"}, "dependencies": {"@prisma/client": "^5.7.1", "@tanstack/react-query": "^5.17.9", "@tanstack/react-query-devtools": "^5.81.5", "@tanstack/react-table": "^8.11.6", "autoprefixer": "^10.4.21", "chart.js": "^4.4.0", "date-fns": "^3.0.6", "lucide-react": "^0.303.0", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-router-dom": "^6.20.1", "tailwindcss": "^3.3.6", "zustand": "^4.4.7"}, "build": {"appId": "com.almamry.erp", "productName": "Almamry ERP", "directories": {"output": "dist"}, "files": ["dist/**/*", "electron/**/*", "prisma/**/*", "node_modules/@prisma/client/**/*", "node_modules/.prisma/**/*", "package.json"], "extraResources": [{"from": "prisma", "to": "prisma"}, {"from": "node_modules/@prisma/client", "to": "node_modules/@prisma/client"}, {"from": "node_modules/.prisma", "to": "node_modules/.prisma"}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}, {"target": "portable", "arch": ["x64"]}], "signAndEditExecutable": false}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}