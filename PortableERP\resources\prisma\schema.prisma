// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./almamry_erp.db"
}

// إعدادات النظام
model SystemSettings {
  id              String   @id @default(cuid())
  companyName     String
  companyAddress  String?
  companyPhone    String?
  companyEmail    String?
  companyWebsite  String?
  taxNumber       String?
  currency        String   @default("دينار ليبي")
  currencySymbol  String   @default("ل.د")
  fiscalYearStart DateTime @default(dbgenerated("CURRENT_TIMESTAMP"))
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
}

// المستخدمين
model User {
  id        String   @id @default(cuid())
  username  String   @unique
  password  String
  fullName  String
  email     String?  @unique
  role      String   @default("USER") // ADMIN, MANAGER, ACCOUNTANT, SALES, PURCHASE, INVENTORY, HR, USER
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // العلاقات
  salesInvoices      SalesInvoice[]
  purchaseInvoices   PurchaseInvoice[]
  expenses           Expense[]
  journalEntries     JournalEntry[]
  PayrollRecord      PayrollRecord[]
  ManufacturingOrder ManufacturingOrder[]
}

// العملاء
model Customer {
  id          String   @id @default(cuid())
  code        String   @unique
  name        String
  phone       String?
  email       String?
  address     String?
  taxNumber   String?
  creditLimit Float?
  balance     Float    @default(0)
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // العلاقات
  salesInvoices SalesInvoice[]
}

// الموردين
model Supplier {
  id        String   @id @default(cuid())
  code      String   @unique
  name      String
  phone     String?
  email     String?
  address   String?
  taxNumber String?
  balance   Float    @default(0)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // العلاقات
  purchaseInvoices PurchaseInvoice[]
}

// الأصناف
model Item {
  id              String   @id @default(cuid())
  code            String   @unique
  name            String
  description     String?
  category        String?
  unit            String
  costPrice       Float    @default(0)
  sellingPrice    Float    @default(0)
  minQuantity     Int      @default(0)
  maxQuantity     Int      @default(0)
  currentQuantity Int      @default(0)
  isActive        Boolean  @default(true)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // العلاقات
  salesInvoiceItems    SalesInvoiceItem[]
  purchaseInvoiceItems PurchaseInvoiceItem[]
  inventoryMovements   InventoryMovement[]
  ManufacturingItem    ManufacturingItem[]
}

// فواتير المبيعات
model SalesInvoice {
  id            String    @id @default(cuid())
  invoiceNumber String    @unique
  invoiceDate   DateTime
  dueDate       DateTime?
  customerId    String
  userId        String
  subtotal      Float     @default(0)
  taxAmount     Float     @default(0)
  discount      Float     @default(0)
  total         Float     @default(0)
  paidAmount    Float     @default(0)
  status        String    @default("DRAFT") // DRAFT, CONFIRMED, PAID, CANCELLED
  notes         String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // العلاقات
  customer Customer           @relation(fields: [customerId], references: [id])
  user     User               @relation(fields: [userId], references: [id])
  items    SalesInvoiceItem[]
}

model SalesInvoiceItem {
  id        String @id @default(cuid())
  invoiceId String
  itemId    String
  quantity  Int
  unitPrice Float
  discount  Float  @default(0)
  total     Float

  // العلاقات
  invoice SalesInvoice @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  item    Item         @relation(fields: [itemId], references: [id])
}

// فواتير المشتريات
model PurchaseInvoice {
  id            String    @id @default(cuid())
  invoiceNumber String    @unique
  invoiceDate   DateTime
  dueDate       DateTime?
  supplierId    String
  userId        String
  subtotal      Float     @default(0)
  taxAmount     Float     @default(0)
  discount      Float     @default(0)
  total         Float     @default(0)
  paidAmount    Float     @default(0)
  status        String    @default("DRAFT") // DRAFT, CONFIRMED, PAID, CANCELLED
  notes         String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // العلاقات
  supplier Supplier              @relation(fields: [supplierId], references: [id])
  user     User                  @relation(fields: [userId], references: [id])
  items    PurchaseInvoiceItem[]
}

model PurchaseInvoiceItem {
  id        String @id @default(cuid())
  invoiceId String
  itemId    String
  quantity  Int
  unitPrice Float
  discount  Float  @default(0)
  total     Float

  // العلاقات
  invoice PurchaseInvoice @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  item    Item            @relation(fields: [itemId], references: [id])
}

// حركة المخزون
model InventoryMovement {
  id           String   @id @default(cuid())
  itemId       String
  movementType String // IN, OUT, ADJUSTMENT
  quantity     Int
  reference    String?
  referenceId  String?
  notes        String?
  createdAt    DateTime @default(now())

  // العلاقات
  item Item @relation(fields: [itemId], references: [id])
}

// الحسابات
model Account {
  id        String   @id @default(cuid())
  code      String   @unique
  name      String
  type      String // ASSET, LIABILITY, EQUITY, REVENUE, EXPENSE
  parentId  String?
  balance   Float    @default(0)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // العلاقات
  parent        Account?           @relation("AccountHierarchy", fields: [parentId], references: [id])
  children      Account[]          @relation("AccountHierarchy")
  debitEntries  JournalEntryItem[] @relation("DebitAccount")
  creditEntries JournalEntryItem[] @relation("CreditAccount")
}

// دفتر اليومية
model JournalEntry {
  id          String   @id @default(cuid())
  entryNumber String   @unique
  entryDate   DateTime
  description String
  reference   String?
  referenceId String?
  userId      String
  isPosted    Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // العلاقات
  user  User               @relation(fields: [userId], references: [id])
  items JournalEntryItem[]
}

model JournalEntryItem {
  id              String  @id @default(cuid())
  entryId         String
  debitAccountId  String?
  creditAccountId String?
  debitAmount     Float   @default(0)
  creditAmount    Float   @default(0)
  description     String?

  // العلاقات
  entry         JournalEntry @relation(fields: [entryId], references: [id], onDelete: Cascade)
  debitAccount  Account?     @relation("DebitAccount", fields: [debitAccountId], references: [id])
  creditAccount Account?     @relation("CreditAccount", fields: [creditAccountId], references: [id])
}

// الموظفين
model Employee {
  id         String   @id @default(cuid())
  code       String   @unique
  fullName   String
  phone      String?
  email      String?
  address    String?
  position   String?
  department String?
  hireDate   DateTime
  isActive   Boolean  @default(true)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  // العلاقات
  payrollRecords PayrollRecord[]
}

// سجلات الرواتب
model PayrollRecord {
  id          String   @id @default(cuid())
  employeeId  String
  userId      String
  month       Int
  year        Int
  basicSalary Float
  allowances  Float    @default(0)
  deductions  Float    @default(0)
  netSalary   Float
  notes       String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // العلاقات
  employee Employee @relation(fields: [employeeId], references: [id])
  user     User     @relation(fields: [userId], references: [id])
}

// أوامر التصنيع
model ManufacturingOrder {
  id          String    @id @default(cuid())
  orderNumber String    @unique
  orderDate   DateTime
  dueDate     DateTime?
  description String
  userId      String
  status      String    @default("DRAFT") // DRAFT, IN_PROGRESS, COMPLETED, CANCELLED
  totalCost   Float     @default(0)
  notes       String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // العلاقات
  user  User                @relation(fields: [userId], references: [id])
  items ManufacturingItem[]
}

model ManufacturingItem {
  id        String  @id @default(cuid())
  orderId   String
  itemId    String
  quantity  Int
  unitCost  Float
  totalCost Float
  notes     String?

  // العلاقات
  order ManufacturingOrder @relation(fields: [orderId], references: [id], onDelete: Cascade)
  item  Item               @relation(fields: [itemId], references: [id])
}

// المصروفات
model Expense {
  id            String   @id @default(cuid())
  expenseNumber String   @unique
  expenseDate   DateTime
  description   String
  amount        Float
  category      String?
  userId        String
  notes         String?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // العلاقات
  user User @relation(fields: [userId], references: [id])
}
