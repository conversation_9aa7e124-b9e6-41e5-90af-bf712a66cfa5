import { PrismaClient } from '@prisma/client';
const prisma = new PrismaClient();

async function main() {
  // إضافة إعدادات النظام الافتراضية
  await prisma.settings.upsert({
    where: { id: 1 },
    update: {},
    create: {
      id: 1,
      companyName: 'شركة الزجاج والألومنيوم',
      fiscalYear: '2024',
      currency: 'SAR',
    },
  });

  // إضافة مستخدم admin الافتراضي
  await prisma.user.upsert({
    where: { username: 'admin' },
    update: {},
    create: {
      username: 'admin',
      password: 'admin', // كلمة مرور افتراضية (يمكنك تغييرها)
      role: 'ADMIN',
      name: 'مدير النظام',
      fullName: 'مدير النظام',
    },
  });

  console.log('✅ تم إضافة بيانات admin وإعدادات النظام بنجاح');
}

main().catch((e) => {
  console.error(e);
  process.exit(1);
}).finally(() => {
  prisma.$disconnect();
});