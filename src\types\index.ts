// أنواع المستخدمين
export interface User {
  id: string
  username: string
  fullName: string
  email?: string
  role: UserRole
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export enum UserRole {
  ADMIN = 'ADMIN',
  MANAGER = 'MANAGER',
  ACCOUNTANT = 'ACCOUNTANT',
  SALES = 'SALES',
  PURCHASE = 'PURCHASE',
  INVENTORY = 'INVENTORY',
  HR = 'HR',
  USER = 'USER'
}

// أنواع العملاء
export interface Customer {
  id: string
  code: string
  name: string
  phone?: string
  email?: string
  address?: string
  taxNumber?: string
  creditLimit?: number
  balance: number
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

// أنواع الموردين
export interface Supplier {
  id: string
  code: string
  name: string
  phone?: string
  email?: string
  address?: string
  taxNumber?: string
  balance: number
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

// أنواع الأصناف
export interface Item {
  id: string
  code: string
  name: string
  description?: string
  category?: string
  unit: string
  costPrice: number
  sellingPrice: number
  minQuantity: number
  maxQuantity: number
  currentQuantity: number
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

// أنواع فواتير المبيعات
export interface SalesInvoice {
  id: string
  invoiceNumber: string
  invoiceDate: Date
  dueDate?: Date
  customerId: string
  userId: string
  subtotal: number
  taxAmount: number
  discount: number
  total: number
  paidAmount: number
  status: InvoiceStatus
  notes?: string
  createdAt: Date
  updatedAt: Date
  customer?: Customer
  user?: User
  items?: SalesInvoiceItem[]
}

export interface SalesInvoiceItem {
  id: string
  invoiceId: string
  itemId: string
  quantity: number
  unitPrice: number
  discount: number
  total: number
  item?: Item
}

// أنواع فواتير المشتريات
export interface PurchaseInvoice {
  id: string
  invoiceNumber: string
  invoiceDate: Date
  dueDate?: Date
  supplierId: string
  userId: string
  subtotal: number
  taxAmount: number
  discount: number
  total: number
  paidAmount: number
  status: InvoiceStatus
  notes?: string
  createdAt: Date
  updatedAt: Date
  supplier?: Supplier
  user?: User
  items?: PurchaseInvoiceItem[]
}

export interface PurchaseInvoiceItem {
  id: string
  invoiceId: string
  itemId: string
  quantity: number
  unitPrice: number
  discount: number
  total: number
  item?: Item
}

export enum InvoiceStatus {
  DRAFT = 'DRAFT',
  CONFIRMED = 'CONFIRMED',
  PAID = 'PAID',
  CANCELLED = 'CANCELLED'
}

// أنواع حركة المخزون
export interface InventoryMovement {
  id: string
  itemId: string
  movementType: MovementType
  quantity: number
  reference?: string
  referenceId?: string
  notes?: string
  createdAt: Date
  item?: Item
}

export enum MovementType {
  IN = 'IN',
  OUT = 'OUT',
  ADJUSTMENT = 'ADJUSTMENT'
}

// أنواع الحسابات
export interface Account {
  id: string
  code: string
  name: string
  type: AccountType
  parentId?: string
  balance: number
  isActive: boolean
  createdAt: Date
  updatedAt: Date
  parent?: Account
  children?: Account[]
}

export enum AccountType {
  ASSET = 'ASSET',
  LIABILITY = 'LIABILITY',
  EQUITY = 'EQUITY',
  REVENUE = 'REVENUE',
  EXPENSE = 'EXPENSE'
}

// أنواع دفتر اليومية
export interface JournalEntry {
  id: string
  entryNumber: string
  entryDate: Date
  description: string
  reference?: string
  referenceId?: string
  userId: string
  isPosted: boolean
  createdAt: Date
  updatedAt: Date
  user?: User
  items?: JournalEntryItem[]
}

export interface JournalEntryItem {
  id: string
  entryId: string
  accountId: string
  debitAccountId?: string
  creditAccountId?: string
  debitAmount: number
  creditAmount: number
  description?: string
  account?: Account
  debitAccount?: Account
  creditAccount?: Account
}

// أنواع الموظفين
export interface Employee {
  id: string
  code: string
  fullName: string
  phone?: string
  email?: string
  address?: string
  hireDate: Date
  salary: number
  position?: string
  department?: string
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

// أنواع الرواتب
export interface PayrollRecord {
  id: string
  employeeId: string
  userId: string
  month: number
  year: number
  basicSalary: number
  allowances: number
  deductions: number
  netSalary: number
  isPaid: boolean
  paidDate?: Date
  notes?: string
  createdAt: Date
  updatedAt: Date
  employee?: Employee
  user?: User
}

// أنواع أوامر التصنيع
export interface ManufacturingOrder {
  id: string
  orderNumber: string
  orderDate: Date
  dueDate?: Date
  userId: string
  status: ManufacturingStatus
  totalCost: number
  notes?: string
  createdAt: Date
  updatedAt: Date
  user?: User
  items?: ManufacturingItem[]
}

export interface ManufacturingItem {
  id: string
  orderId: string
  itemId: string
  quantity: number
  unitCost: number
  totalCost: number
  isRawMaterial: boolean
  item?: Item
}

export enum ManufacturingStatus {
  DRAFT = 'DRAFT',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

// أنواع المصروفات
export interface Expense {
  id: string
  expenseNumber: string
  expenseDate: Date
  category: string
  amount: number
  description?: string
  userId: string
  isPaid: boolean
  paidDate?: Date
  createdAt: Date
  updatedAt: Date
  user?: User
}

// أنواع إعدادات النظام
export interface SystemSettings {
  id: string
  companyName: string
  companyAddress?: string
  companyPhone?: string
  companyEmail?: string
  companyWebsite?: string
  taxNumber?: string
  currency: string
  currencySymbol: string
  fiscalYearStart: Date
  createdAt: Date
  updatedAt: Date
}

// أنواع API Response
export interface ApiResponse<T> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

// أنواع النماذج
export interface LoginForm {
  username: string
  password: string
}

export interface CustomerForm {
  code: string
  name: string
  phone?: string
  email?: string
  address?: string
  taxNumber?: string
  creditLimit?: number
}

export interface SupplierForm {
  code: string
  name: string
  phone?: string
  email?: string
  address?: string
  taxNumber?: string
}

export interface ItemForm {
  code: string
  name: string
  description?: string
  category?: string
  unit: string
  costPrice: number
  sellingPrice: number
  minQuantity: number
  maxQuantity: number
}

export interface SalesInvoiceForm {
  invoiceDate: Date
  dueDate?: Date
  customerId: string
  items: SalesInvoiceItemForm[]
  taxAmount: number
  discount: number
  notes?: string
}

export interface SalesInvoiceItemForm {
  itemId: string
  quantity: number
  unitPrice: number
  discount: number
}

export interface PurchaseInvoiceForm {
  invoiceDate: Date
  dueDate?: Date
  supplierId: string
  items: PurchaseInvoiceItemForm[]
  taxAmount: number
  discount: number
  notes?: string
}

export interface PurchaseInvoiceItemForm {
  itemId: string
  quantity: number
  unitPrice: number
  discount: number
}

export interface JournalEntryForm {
  entryDate: Date
  description: string
  reference?: string
  items: JournalEntryItemForm[]
}

export interface JournalEntryItemForm {
  accountId: string
  debitAmount: number
  creditAmount: number
  description?: string
}

export interface EmployeeForm {
  code: string
  fullName: string
  phone?: string
  email?: string
  address?: string
  hireDate: Date
  salary: number
  position?: string
  department?: string
}

export interface PayrollForm {
  employeeId: string
  month: number
  year: number
  basicSalary: number
  allowances: number
  deductions: number
  notes?: string
}

export interface ManufacturingOrderForm {
  orderDate: Date
  dueDate?: Date
  items: ManufacturingItemForm[]
  notes?: string
}

export interface ManufacturingItemForm {
  itemId: string
  quantity: number
  unitCost: number
  isRawMaterial: boolean
}

export interface ExpenseForm {
  expenseDate: Date
  category: string
  amount: number
  description?: string
}

export interface CompanySettingsForm {
  companyName: string
  companyAddress?: string
  companyPhone?: string
  companyEmail?: string
  companyWebsite?: string
  taxNumber?: string
  currency: string
  currencySymbol: string
  fiscalYearStart: Date
} 