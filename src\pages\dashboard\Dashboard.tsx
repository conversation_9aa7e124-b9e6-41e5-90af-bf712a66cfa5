import React, { useEffect } from 'react'
import { 
  ShoppingCart, 
  Package, 
  Warehouse, 
  Calculator, 
  Users, 
  Factory, 
  CreditCard,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Package as PackageIcon
} from 'lucide-react'

const Dashboard: React.FC = () => {
  // بيانات وهمية للإحصائيات
  const stats = [
    {
      title: 'إجمالي المبيعات',
      value: '125,000',
      currency: 'ل.د',
      change: '+12.5%',
      changeType: 'up',
      icon: ShoppingCart,
      color: 'bg-green-500'
    },
    {
      title: 'إجمالي المشتريات',
      value: '85,000',
      currency: 'ل.د',
      change: '+8.3%',
      changeType: 'up',
      icon: Package,
      color: 'bg-blue-500'
    },
    {
      title: 'المخزون الحالي',
      value: '1,250',
      currency: 'صنف',
      change: '-2.1%',
      changeType: 'down',
      icon: Warehouse,
      color: 'bg-orange-500'
    },
    {
      title: 'الأرباح',
      value: '40,000',
      currency: 'ل.د',
      change: '+15.2%',
      changeType: 'up',
      icon: TrendingUp,
      color: 'bg-purple-500'
    }
  ]

  const recentActivities = [
    {
      id: 1,
      type: 'sale',
      title: 'فاتورة مبيعات جديدة',
      description: 'تم إنشاء فاتورة مبيعات رقم INV-001',
      amount: '15,000 ل.د',
      time: 'منذ 5 دقائق',
      status: 'success'
    },
    {
      id: 2,
      type: 'purchase',
      title: 'فاتورة مشتريات جديدة',
      description: 'تم إنشاء فاتورة مشتريات رقم PO-001',
      amount: '8,500 ل.د',
      time: 'منذ 15 دقيقة',
      status: 'info'
    },
    {
      id: 3,
      type: 'inventory',
      title: 'تحديث المخزون',
      description: 'تم تحديث كمية الصنف "لابتوب HP"',
      amount: '+50 وحدة',
      time: 'منذ ساعة',
      status: 'warning'
    },
    {
      id: 4,
      type: 'payment',
      title: 'دفع مستحق',
      description: 'تم دفع فاتورة المورد "شركة التقنية"',
      amount: '12,000 ل.د',
      time: 'منذ ساعتين',
      status: 'success'
    }
  ]

  const quickActions = [
    {
      title: 'فاتورة مبيعات جديدة',
      icon: ShoppingCart,
      color: 'bg-green-500',
      path: '/sales/invoices/new'
    },
    {
      title: 'فاتورة مشتريات جديدة',
      icon: Package,
      color: 'bg-blue-500',
      path: '/purchases/invoices/new'
    },
    {
      title: 'إضافة صنف جديد',
      icon: PackageIcon,
      color: 'bg-orange-500',
      path: '/inventory/items/new'
    },
    {
      title: 'قيد يومية جديد',
      icon: Calculator,
      color: 'bg-purple-500',
      path: '/accounting/journal/new'
    },
    {
      title: 'إضافة موظف جديد',
      icon: Users,
      color: 'bg-indigo-500',
      path: '/payroll/employees/new'
    },
    {
      title: 'أمر تصنيع جديد',
      icon: Factory,
      color: 'bg-teal-500',
      path: '/manufacturing/orders/new'
    }
  ]

  return (
    <div className="dashboard-standalone-wrapper">
      {/* Sidebar ثابتة من تصميم standalone */}
      <aside className="sidebar">
        <div className="sidebar-header p-4 border-b">
          <h2 className="text-xl font-bold text-blue-700">Almamry ERP</h2>
          <p className="text-xs text-gray-500 mt-1">نظام إدارة موارد المؤسسات</p>
        </div>
        <nav className="sidebar-nav mt-4">
          <a href="#" className="nav-link active">لوحة التحكم</a>
          <a href="#" className="nav-link">المبيعات</a>
          <a href="#" className="nav-link">المشتريات</a>
          <a href="#" className="nav-link">المخزون</a>
          <a href="#" className="nav-link">المحاسبة</a>
          <a href="#" className="nav-link">الموظفون</a>
          <a href="#" className="nav-link">التصنيع</a>
          <a href="#" className="nav-link">الإعدادات</a>
        </nav>
      </aside>
      {/* Main Content */}
      <main className="main-content p-6">
        {/* العنوان */}
        <div>
          <h1 className="text-2xl font-bold text-gray-800">لوحة التحكم</h1>
          <p className="text-gray-600 mt-1">مرحباً بك في نظام Almamry ERP</p>
        </div>

        {/* الإحصائيات */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-6">
          {stats.map((stat, index) => (
            <div key={index} className="card">
              <div className="card-body">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-800 mt-1">
                      {stat.value} {stat.currency}
                    </p>
                  </div>
                  <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${stat.color}`}>
                    <stat.icon className="w-6 h-6 text-white" />
                  </div>
                </div>
                <div className="flex items-center mt-4">
                  {stat.changeType === 'up' ? (
                    <TrendingUp className="w-4 h-4 text-green-500 ml-1" />
                  ) : (
                    <TrendingDown className="w-4 h-4 text-red-500 ml-1" />
                  )}
                  <span className={`text-sm font-medium ${
                    stat.changeType === 'up' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {stat.change}
                  </span>
                  <span className="text-sm text-gray-500 mr-2">من الشهر الماضي</span>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* المحتوى الرئيسي */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* الإجراءات السريعة */}
          <div className="lg:col-span-1">
            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-semibold text-gray-800">الإجراءات السريعة</h3>
              </div>
              <div className="card-body">
                <div className="grid grid-cols-2 gap-4">
                  {quickActions.map((action, index) => (
                    <button
                      key={index}
                      className="flex flex-col items-center p-4 rounded-lg border border-gray-200 hover:border-primary-300 hover:bg-primary-50 transition-colors duration-200"
                    >
                      <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${action.color} mb-3`}>
                        <action.icon className="w-5 h-5 text-white" />
                      </div>
                      <span className="text-sm font-medium text-gray-700 text-center">
                        {action.title}
                      </span>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* النشاطات الأخيرة */}
          <div className="lg:col-span-2">
            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-semibold text-gray-800">النشاطات الأخيرة</h3>
              </div>
              <div className="card-body">
                <div className="space-y-4">
                  {recentActivities.map((activity) => (
                    <div key={activity.id} className="flex items-center space-x-4 space-x-reverse p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                      <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                        activity.status === 'success' ? 'bg-green-100' :
                        activity.status === 'warning' ? 'bg-yellow-100' :
                        activity.status === 'info' ? 'bg-blue-100' : 'bg-gray-100'
                      }`}>
                        {activity.type === 'sale' && <ShoppingCart className="w-5 h-5 text-green-600" />}
                        {activity.type === 'purchase' && <Package className="w-5 h-5 text-blue-600" />}
                        {activity.type === 'inventory' && <Warehouse className="w-5 h-5 text-orange-600" />}
                        {activity.type === 'payment' && <DollarSign className="w-5 h-5 text-green-600" />}
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-800">{activity.title}</p>
                        <p className="text-sm text-gray-600">{activity.description}</p>
                        <p className="text-xs text-gray-500 mt-1">{activity.time}</p>
                      </div>
                      <div className="text-left">
                        <p className="text-sm font-medium text-gray-800">{activity.amount}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* الرسوم البيانية */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-semibold text-gray-800">المبيعات الشهرية</h3>
            </div>
            <div className="card-body">
              <div className="h-64 flex items-center justify-center text-gray-500">
                رسم بياني للمبيعات الشهرية
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-semibold text-gray-800">توزيع المخزون</h3>
            </div>
            <div className="card-body">
              <div className="h-64 flex items-center justify-center text-gray-500">
                رسم بياني لتوزيع المخزون
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}

export default Dashboard 