@echo off
chcp 65001 >nul
title نظام المعماري ERP - النسخة المحمولة

echo.
echo ========================================
echo   نظام إدارة موارد المؤسسات
echo   شركة المعماري لصناعة الزجاج والألومنيوم
echo   النسخة المحمولة
echo ========================================
echo.

echo 🚀 بدء تشغيل النظام...
echo.

echo [1/3] التحقق من التبعيات...
if not exist "node_modules" (
    echo 📦 تثبيت التبعيات...
    npm install --silent
    if errorlevel 1 (
        echo ❌ فشل في تثبيت التبعيات
        pause
        exit /b 1
    )
)
echo ✅ التبعيات جاهزة

echo.
echo [2/3] التحقق من قاعدة البيانات...
if not exist "prisma\almamry_erp.db" (
    echo 🗄️ إنشاء قاعدة البيانات...
    npx prisma db push --accept-data-loss --silent
    if errorlevel 1 (
        echo ❌ فشل في إنشاء قاعدة البيانات
        pause
        exit /b 1
    )
)
echo ✅ قاعدة البيانات جاهزة

echo.
echo [3/3] تشغيل النظام...
echo.
echo 🌐 النظام سيفتح في المتصفح على: http://localhost:5173
echo 💻 تطبيق Electron سيفتح تلقائياً
echo.
echo 🔐 بيانات الدخول: admin / admin123
echo.
echo ⚠️  لا تغلق هذا النافذة حتى تنتهي من استخدام النظام
echo.

npm start

echo.
echo 👋 تم إغلاق النظام
pause 