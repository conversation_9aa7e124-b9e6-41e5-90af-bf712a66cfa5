import React from 'react'

const LoadingScreen: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-primary-100 flex items-center justify-center">
      <div className="text-center">
        {/* شعار التطبيق */}
        <div className="mb-8">
          <div className="w-24 h-24 mx-auto bg-gradient-libya rounded-full flex items-center justify-center shadow-lg">
            <span className="text-white text-2xl font-bold">أ</span>
          </div>
          <h1 className="text-3xl font-bold text-gray-800 mt-4">Almamry ERP</h1>
          <p className="text-gray-600 mt-2">نظام إدارة موارد المؤسسات</p>
        </div>

        {/* مؤشر التحميل */}
        <div className="flex items-center justify-center space-x-2 space-x-reverse">
          <div className="w-3 h-3 bg-primary-600 rounded-full animate-bounce"></div>
          <div className="w-3 h-3 bg-primary-600 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
          <div className="w-3 h-3 bg-primary-600 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
        </div>

        <p className="text-gray-500 mt-4">جاري تحميل النظام...</p>
      </div>
    </div>
  )
}

export default LoadingScreen 