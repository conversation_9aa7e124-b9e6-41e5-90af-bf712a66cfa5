# نظام إدارة موارد المؤسسات - النسخة المحمولة
## شركة المعماري لصناعة الزجاج والألومنيوم

### 🚀 التشغيل السريع

#### الطريقة الأولى: مل<PERSON> (Windows)
```bash
# انقر مرتين على الملف
start-system.bat
```

#### الطريقة الثانية: ملف PowerShell (Windows)
```powershell
# انقر بزر الماوس الأيمن واختر "Run with PowerShell"
.\start-system.ps1
```

#### الطريقة الثالثة: الأوامر اليدوية
```bash
# 1. تثبيت التبعيات (مرة واحدة فقط)
npm install

# 2. إنشاء قاعدة البيانات (مرة واحدة فقط)
npx prisma db push

# 3. تشغيل النظام
npm start
```

### 📋 المتطلبات

- **Node.js** (الإصدار 18 أو أحدث)
  - تحميل من: https://nodejs.org/
  - اختيار النسخة LTS

### 🔧 الميزات

#### ✅ نظام محمول بالكامل
- لا يحتاج تثبيت
- يعمل من أي مجلد
- قاعدة بيانات SQLite محلية
- لا يحتاج إنترنت

#### ✅ واجهة مستخدم عربية
- تصميم حديث ومتجاوب
- دعم كامل للغة العربية
- واجهة سهلة الاستخدام

#### ✅ جميع الوحدات جاهزة
- **المبيعات**: العملاء، فواتير المبيعات
- **المشتريات**: الموردين، فواتير المشتريات
- **المخزون**: الأصناف، حركات المخزون
- **التصنيع**: أوامر التصنيع
- **الموارد البشرية**: الموظفين، الرواتب
- **المحاسبة**: دليل الحسابات، القيود اليومية
- **المصروفات**: إدارة المصروفات
- **الإعدادات**: إعدادات الشركة والنظام

### 🗄️ قاعدة البيانات

#### الوصول لقاعدة البيانات
```bash
# فتح Prisma Studio لتصفح البيانات
npx prisma studio
```

#### معلومات قاعدة البيانات
- **النوع**: SQLite
- **الموقع**: `prisma/almamry_erp.db`
- **النسخ الاحتياطي**: يدوي (نسخ الملف)

### 🔐 تسجيل الدخول

#### بيانات الدخول الافتراضية
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`

### 📁 هيكل المشروع

```
AlmamryERP/
├── start-system.bat          # ملف تشغيل سريع (Windows)
├── start-system.ps1          # ملف تشغيل PowerShell
├── src/                      # كود التطبيق
│   ├── pages/               # صفحات النظام
│   ├── components/          # المكونات
│   ├── stores/              # إدارة الحالة
│   └── services/            # الخدمات
├── prisma/                  # قاعدة البيانات
│   ├── schema.prisma        # مخطط قاعدة البيانات
│   └── almamry_erp.db       # ملف قاعدة البيانات
└── electron/                # تطبيق Electron
```

### 🛠️ التطوير

#### تشغيل وضع التطوير
```bash
# تشغيل Vite فقط (للتطوير)
npm run dev

# تشغيل Electron فقط
npm run electron

# تشغيل كلاهما معاً
npm start
```

#### إعادة بناء قاعدة البيانات
```bash
# حذف قاعدة البيانات الحالية
rm prisma/almamry_erp.db

# إعادة إنشاء قاعدة البيانات
npx prisma db push
```

### 📊 الميزات المتقدمة

#### 📈 لوحة التحكم
- مؤشرات الأداء الرئيسية
- رسوم بيانية تفاعلية
- إحصائيات فورية

#### 🔄 النسخ الاحتياطي
- تصدير البيانات
- استيراد البيانات
- نسخ احتياطي تلقائي

#### 🎨 التخصيص
- إعدادات الشركة
- تخصيص الواجهة
- إدارة المستخدمين

### 🆘 استكشاف الأخطاء

#### مشكلة: شاشة بيضاء في Electron
```bash
# الحل: تشغيل من المتصفح أولاً
npm run dev
# ثم فتح http://localhost:5173
```

#### مشكلة: خطأ في قاعدة البيانات
```bash
# الحل: إعادة إنشاء قاعدة البيانات
npx prisma db push
```

#### مشكلة: تبعيات مفقودة
```bash
# الحل: إعادة تثبيت التبعيات
npm install
```

### 📞 الدعم

للمساعدة أو الإبلاغ عن مشاكل:
- تحقق من ملف `README.md` الرئيسي
- راجع سجلات الأخطاء في Terminal
- تأكد من تثبيت Node.js بشكل صحيح

### 🔄 التحديثات

لتحديث النظام:
1. احفظ نسخة احتياطية من قاعدة البيانات
2. احذف مجلد `node_modules`
3. شغل `npm install` مرة أخرى
4. شغل `npx prisma db push` إذا تم تحديث قاعدة البيانات

---

**تم تطوير هذا النظام خصيصاً لشركة المعماري لصناعة الزجاج والألومنيوم** 