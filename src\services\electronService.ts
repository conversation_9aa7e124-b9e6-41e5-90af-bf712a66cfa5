// خدمة للتعامل مع قاعدة البيانات عبر Electron
export const electronService = {
  // العملاء
  getCustomers: async () => {
    if (window.electronAPI) {
      return await window.electronAPI.database.getCustomers();
    }
    throw new Error('Electron API غير متاح');
  },

  createCustomer: async (data: any) => {
    if (window.electronAPI) {
      return await window.electronAPI.database.createCustomer(data);
    }
    throw new Error('Electron API غير متاح');
  },

  updateCustomer: async (id: string, data: any) => {
    if (window.electronAPI) {
      return await window.electronAPI.database.updateCustomer(id, data);
    }
    throw new Error('Electron API غير متاح');
  },

  deleteCustomer: async (id: string) => {
    if (window.electronAPI) {
      return await window.electronAPI.database.deleteCustomer(id);
    }
    throw new Error('Electron API غير متاح');
  },

  // الموردين
  getSuppliers: async () => {
    if (window.electronAPI) {
      return await window.electronAPI.database.getSuppliers();
    }
    throw new Error('Electron API غير متاح');
  },

  createSupplier: async (data: any) => {
    if (window.electronAPI) {
      return await window.electronAPI.database.createSupplier(data);
    }
    throw new Error('Electron API غير متاح');
  },

  updateSupplier: async (id: string, data: any) => {
    if (window.electronAPI) {
      return await window.electronAPI.database.updateSupplier(id, data);
    }
    throw new Error('Electron API غير متاح');
  },

  deleteSupplier: async (id: string) => {
    if (window.electronAPI) {
      return await window.electronAPI.database.deleteSupplier(id);
    }
    throw new Error('Electron API غير متاح');
  },

  // الأصناف
  getItems: async () => {
    if (window.electronAPI) {
      return await window.electronAPI.database.getItems();
    }
    throw new Error('Electron API غير متاح');
  },

  createItem: async (data: any) => {
    if (window.electronAPI) {
      return await window.electronAPI.database.createItem(data);
    }
    throw new Error('Electron API غير متاح');
  },

  updateItem: async (id: string, data: any) => {
    if (window.electronAPI) {
      return await window.electronAPI.database.updateItem(id, data);
    }
    throw new Error('Electron API غير متاح');
  },

  deleteItem: async (id: string) => {
    if (window.electronAPI) {
      return await window.electronAPI.database.deleteItem(id);
    }
    throw new Error('Electron API غير متاح');
  },

  // فواتير المبيعات
  getSalesInvoices: async () => {
    if (window.electronAPI) {
      return await window.electronAPI.database.getSalesInvoices();
    }
    throw new Error('Electron API غير متاح');
  },

  createSalesInvoice: async (data: any) => {
    if (window.electronAPI) {
      return await window.electronAPI.database.createSalesInvoice(data);
    }
    throw new Error('Electron API غير متاح');
  },

  // فواتير المشتريات
  getPurchaseInvoices: async () => {
    if (window.electronAPI) {
      return await window.electronAPI.database.getPurchaseInvoices();
    }
    throw new Error('Electron API غير متاح');
  },

  createPurchaseInvoice: async (data: any) => {
    if (window.electronAPI) {
      return await window.electronAPI.database.createPurchaseInvoice(data);
    }
    throw new Error('Electron API غير متاح');
  },

  // الإحصائيات
  getStats: async () => {
    if (window.electronAPI) {
      return await window.electronAPI.database.getStats();
    }
    throw new Error('Electron API غير متاح');
  },

  // النسخ الاحتياطي والاستعادة
  backup: async () => {
    if (window.electronAPI) {
      return await window.electronAPI.database.backup();
    }
    throw new Error('Electron API غير متاح');
  },

  restore: async (filePath: string) => {
    if (window.electronAPI) {
      return await window.electronAPI.database.restore(filePath);
    }
    throw new Error('Electron API غير متاح');
  },

  // التحقق من توفر Electron
  isElectronAvailable: () => {
    return !!window.electronAPI;
  }
}; 