import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { Eye, EyeOff, Lock, User } from 'lucide-react'
import { useAuthStore } from '@/stores/authStore'
import { LoginForm } from '@/types'

const Login: React.FC = () => {
  const { login, isLoading } = useAuthStore()
  const [showPassword, setShowPassword] = useState(false)
  const [error, setError] = useState('')

  const {
    register,
    handleSubmit,
    formState: { errors }
  } = useForm<LoginForm>()

  const onSubmit = async (data: LoginForm) => {
    setError('')
    const success = await login(data.username, data.password)
    
    if (!success) {
      setError('اسم المستخدم أو كلمة المرور غير صحيحة')
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-primary-100 flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        {/* شعار التطبيق */}
        <div className="text-center mb-8">
          <div className="w-20 h-20 mx-auto bg-gradient-libya rounded-full flex items-center justify-center shadow-lg mb-4">
            <span className="text-white text-2xl font-bold">أ</span>
          </div>
          <h1 className="text-3xl font-bold text-gray-800 mb-2">Almamry ERP</h1>
          <p className="text-gray-600">نظام إدارة موارد المؤسسات</p>
        </div>

        {/* نموذج تسجيل الدخول */}
        <div className="bg-white rounded-lg shadow-lg p-8">
          <div className="text-center mb-6">
            <h2 className="text-2xl font-bold text-gray-800">تسجيل الدخول</h2>
            <p className="text-gray-600 mt-2">أدخل بيانات الدخول الخاصة بك</p>
          </div>

          {error && (
            <div className="alert-danger mb-4">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* اسم المستخدم */}
            <div className="form-group">
              <label className="form-label">اسم المستخدم</label>
              <div className="relative">
                <User className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="text"
                  {...register('username', { required: 'اسم المستخدم مطلوب' })}
                  className={`input pr-10 ${errors.username ? 'input-error' : ''}`}
                  placeholder="أدخل اسم المستخدم"
                />
              </div>
              {errors.username && (
                <p className="form-error">{errors.username.message}</p>
              )}
            </div>

            {/* كلمة المرور */}
            <div className="form-group">
              <label className="form-label">كلمة المرور</label>
              <div className="relative">
                <Lock className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type={showPassword ? 'text' : 'password'}
                  {...register('password', { required: 'كلمة المرور مطلوبة' })}
                  className={`input pr-10 ${errors.password ? 'input-error' : ''}`}
                  placeholder="أدخل كلمة المرور"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? (
                    <EyeOff className="w-5 h-5" />
                  ) : (
                    <Eye className="w-5 h-5" />
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="form-error">{errors.password.message}</p>
              )}
            </div>

            {/* زر تسجيل الدخول */}
            <button
              type="submit"
              disabled={isLoading}
              className="w-full btn-primary py-3 text-base font-medium disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin ml-2"></div>
                  جاري تسجيل الدخول...
                </div>
              ) : (
                'تسجيل الدخول'
              )}
            </button>
          </form>

          {/* معلومات إضافية */}
          <div className="mt-6 text-center">
            <p className="text-sm text-gray-500">
              بيانات الدخول الافتراضية:
            </p>
            <p className="text-xs text-gray-400 mt-1">
              اسم المستخدم: admin | كلمة المرور: admin
            </p>
          </div>
        </div>

        {/* معلومات النظام */}
        <div className="text-center mt-8">
          <p className="text-sm text-gray-500">الإصدار 1.0.0</p>
          <p className="text-xs text-gray-400 mt-1">© 2024 Almamry ERP. جميع الحقوق محفوظة.</p>
        </div>
      </div>
    </div>
  )
}

export default Login 