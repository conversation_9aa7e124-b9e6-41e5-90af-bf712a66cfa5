// تعريف واجهة Electron API
declare global {
  interface Window {
    electronAPI: {
      getAppVersion: () => Promise<string>;
      getAppName: () => Promise<string>;
      
      // إدارة الملفات
      saveFile: (data: any) => Promise<void>;
      openFile: () => Promise<any>;
      
      // الطباعة
      print: (data: any) => Promise<void>;
      
      // إشعارات
      showNotification: (title: string, body: string) => Promise<void>;
      
      // إعدادات النظام
      getSystemInfo: () => Promise<any>;
      
      // قاعدة البيانات
      database: {
        // العملاء
        getCustomers: () => Promise<any[]>;
        createCustomer: (data: any) => Promise<any>;
        updateCustomer: (id: string, data: any) => Promise<any>;
        deleteCustomer: (id: string) => Promise<void>;
        
        // الموردين
        getSuppliers: () => Promise<any[]>;
        createSupplier: (data: any) => Promise<any>;
        updateSupplier: (id: string, data: any) => Promise<any>;
        deleteSupplier: (id: string) => Promise<void>;
        
        // الأصناف
        getItems: () => Promise<any[]>;
        createItem: (data: any) => Promise<any>;
        updateItem: (id: string, data: any) => Promise<any>;
        deleteItem: (id: string) => Promise<void>;
        
        // فواتير المبيعات
        getSalesInvoices: () => Promise<any[]>;
        createSalesInvoice: (data: any) => Promise<any>;
        
        // فواتير المشتريات
        getPurchaseInvoices: () => Promise<any[]>;
        createPurchaseInvoice: (data: any) => Promise<any>;
        
        // الإحصائيات
        getStats: () => Promise<any>;
        
        // النسخ الاحتياطي والاستعادة
        backup: () => Promise<void>;
        restore: (filePath: string) => Promise<void>;
      };
    };
  }
}

export {}; 