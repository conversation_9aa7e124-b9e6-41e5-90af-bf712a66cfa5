# 🚀 تشغيل سريع - نظام المعماري ERP

## الطريقة الأسهل (انقر مرتين)

```
run-portable.bat
```

## المتطلبات
- Node.js (تحميل من https://nodejs.org/)

## بيانات الدخول
- **المستخدم**: `admin`
- **كلمة المرور**: `admin123`

## الوحدات المتاحة
- 📊 لوحة التحكم
- 👥 العملاء
- 🏭 الموردين  
- 📦 المخزون
- 🏗️ التصنيع
- 👨‍💼 الموظفين
- 💰 المحاسبة
- 💸 المصروفات
- ⚙️ الإعدادات

## قاعدة البيانات
- **النوع**: SQLite (محلية)
- **الموقع**: `prisma/almamry_erp.db`
- **التصفح**: `npx prisma studio`

## النسخ الاحتياطي
انسخ ملف `prisma/almamry_erp.db` إلى مكان آمن

---
**شركة المعماري لصناعة الزجاج والألومنيوم** 