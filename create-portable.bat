@echo off
chcp 65001 >nul
title إنشاء النسخة المحمولة - نظام المعماري ERP

echo.
echo ========================================
echo   إنشاء النسخة المحمولة
echo   نظام إدارة موارد المؤسسات
echo   شركة المعماري لصناعة الزجاج والألومنيوم
echo ========================================
echo.

set PORTABLE_DIR=AlmamryERP-Portable
set DATE_TIME=%date:~-4,4%-%date:~3,2%-%date:~0,2%_%time:~0,2%-%time:~3,2%-%time:~6,2%
set DATE_TIME=%DATE_TIME: =0%

echo [1/5] إنشاء مجلد النسخة المحمولة...
if exist "%PORTABLE_DIR%" (
    echo حذف المجلد القديم...
    rmdir /s /q "%PORTABLE_DIR%"
)
mkdir "%PORTABLE_DIR%"
echo ✅ تم إنشاء المجلد

echo.
echo [2/5] نسخ الملفات الأساسية...
xcopy "src" "%PORTABLE_DIR%\src" /e /i /y
xcopy "electron" "%PORTABLE_DIR%\electron" /e /i /y
xcopy "prisma" "%PORTABLE_DIR%\prisma" /e /i /y
copy "package.json" "%PORTABLE_DIR%\"
copy "package-lock.json" "%PORTABLE_DIR%\"
copy "vite.config.ts" "%PORTABLE_DIR%\"
copy "tsconfig.json" "%PORTABLE_DIR%\"
copy "tsconfig.node.json" "%PORTABLE_DIR%\"
copy "tailwind.config.js" "%PORTABLE_DIR%\"
copy "postcss.config.js" "%PORTABLE_DIR%\"
copy "index.html" "%PORTABLE_DIR%\"
copy "index-standalone.html" "%PORTABLE_DIR%\"
echo ✅ تم نسخ الملفات الأساسية

echo.
echo [3/5] نسخ ملفات التشغيل...
copy "run-portable.bat" "%PORTABLE_DIR%\"
copy "start-system.bat" "%PORTABLE_DIR%\"
copy "start-system.ps1" "%PORTABLE_DIR%\"
copy "QUICK-START.md" "%PORTABLE_DIR%\"
copy "README-PORTABLE.md" "%PORTABLE_DIR%\"
echo ✅ تم نسخ ملفات التشغيل

echo.
echo [4/5] إنشاء ملف README للنسخة المحمولة...
echo # نظام إدارة موارد المؤسسات - النسخة المحمولة > "%PORTABLE_DIR%\README.md"
echo ## شركة المعماري لصناعة الزجاج والألومنيوم >> "%PORTABLE_DIR%\README.md"
echo. >> "%PORTABLE_DIR%\README.md"
echo ### 🚀 التشغيل السريع >> "%PORTABLE_DIR%\README.md"
echo. >> "%PORTABLE_DIR%\README.md"
echo انقر مرتين على الملف: >> "%PORTABLE_DIR%\README.md"
echo ``` >> "%PORTABLE_DIR%\README.md"
echo run-portable.bat >> "%PORTABLE_DIR%\README.md"
echo ``` >> "%PORTABLE_DIR%\README.md"
echo. >> "%PORTABLE_DIR%\README.md"
echo ### 📋 المتطلبات >> "%PORTABLE_DIR%\README.md"
echo - Node.js (الإصدار 18 أو أحدث) >> "%PORTABLE_DIR%\README.md"
echo - تحميل من: https://nodejs.org/ >> "%PORTABLE_DIR%\README.md"
echo. >> "%PORTABLE_DIR%\README.md"
echo ### 🔐 بيانات الدخول >> "%PORTABLE_DIR%\README.md"
echo - **المستخدم**: admin >> "%PORTABLE_DIR%\README.md"
echo - **كلمة المرور**: admin123 >> "%PORTABLE_DIR%\README.md"
echo. >> "%PORTABLE_DIR%\README.md"
echo ### 📅 تاريخ الإنشاء: %DATE_TIME% >> "%PORTABLE_DIR%\README.md"
echo ✅ تم إنشاء ملف README

echo.
echo [5/5] إنشاء ملف ZIP للنسخة المحمولة...
powershell -command "Compress-Archive -Path '%PORTABLE_DIR%' -DestinationPath 'AlmamryERP-Portable-%DATE_TIME%.zip' -Force"
if errorlevel 1 (
    echo ⚠️ فشل في إنشاء ملف ZIP، لكن النسخة المحمولة جاهزة في مجلد %PORTABLE_DIR%
) else (
    echo ✅ تم إنشاء ملف ZIP: AlmamryERP-Portable-%DATE_TIME%.zip
)

echo.
echo ========================================
echo   ✅ تم إنشاء النسخة المحمولة بنجاح
echo ========================================
echo.
echo 📁 موقع النسخة المحمولة: %PORTABLE_DIR%
echo 📦 ملف ZIP: AlmamryERP-Portable-%DATE_TIME%.zip
echo.
echo 🚀 لتشغيل النسخة المحمولة:
echo    1. انسخ مجلد %PORTABLE_DIR% إلى أي مكان
echo    2. انقر مرتين على run-portable.bat
echo.
echo 📋 ملاحظات مهمة:
echo    - تأكد من تثبيت Node.js على الجهاز المستهدف
echo    - النسخة تعمل بشكل كامل بدون إنترنت
echo    - قاعدة البيانات محلية في prisma/almamry_erp.db
echo.
pause 