const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  console.log('🔄 بدء seed البيانات...');

  // إنشاء إعدادات النظام
  const systemSettings = await prisma.systemSettings.upsert({
    where: { id: '1' },
    update: {},
    create: {
      id: '1',
      companyName: 'شركة المعماري لصناعة الزجاج والألومنيوم',
      companyAddress: 'طرابلس، ليبيا',
      companyPhone: '+218-21-1234567',
      companyEmail: '<EMAIL>',
      currency: 'دينار ليبي',
      currencySymbol: 'ل.د',
    },
  });

  console.log('✅ تم إنشاء إعدادات النظام');

  // إنشاء مستخدم المدير
  const adminUser = await prisma.user.upsert({
    where: { username: 'admin' },
    update: {},
    create: {
      username: 'admin',
      password: 'admin123', // في الإنتاج يجب تشفير كلمة المرور
      fullName: 'مدير النظام',
      name: 'مدير النظام',
      email: '<EMAIL>',
      role: 'ADMIN',
    },
  });

  console.log('✅ تم إنشاء مستخدم المدير');

  // إنشاء عملاء تجريبيين
  const customer1 = await prisma.customer.upsert({
    where: { code: 'C001' },
    update: {},
    create: {
      code: 'C001',
      name: 'شركة البناء الحديث',
      phone: '+218-21-1111111',
      email: '<EMAIL>',
      address: 'طرابلس، ليبيا',
      creditLimit: 50000,
      balance: 0,
    },
  });

  const customer2 = await prisma.customer.upsert({
    where: { code: 'C002' },
    update: {},
    create: {
      code: 'C002',
      name: 'مؤسسة الإنشاءات المتطورة',
      phone: '+218-21-2222222',
      email: '<EMAIL>',
      address: 'بنغازي، ليبيا',
      creditLimit: 75000,
      balance: 0,
    },
  });

  console.log('✅ تم إنشاء العملاء التجريبيين');

  console.log('🎉 تم إكمال seed البيانات بنجاح!');
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error('❌ خطأ في seed البيانات:', e);
    await prisma.$disconnect();
    process.exit(1);
  });
