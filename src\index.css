@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    direction: rtl;
    font-family: 'Cairo', sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-900;
  }
  
  * {
    @apply border-gray-200;
  }
}

@layer components {
  /* أزرار مخصصة */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
  }
  
  .btn-success {
    @apply btn bg-success-600 text-white hover:bg-success-700 focus:ring-success-500;
  }
  
  .btn-warning {
    @apply btn bg-warning-600 text-white hover:bg-warning-700 focus:ring-warning-500;
  }
  
  .btn-danger {
    @apply btn bg-danger-600 text-white hover:bg-danger-700 focus:ring-danger-500;
  }
  
  .btn-outline {
    @apply btn border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500;
  }
  
  /* حقول الإدخال */
  .input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
  }
  
  .input-error {
    @apply input border-danger-300 focus:ring-danger-500 focus:border-danger-500;
  }
  
  /* البطاقات */
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-gray-200;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50 rounded-b-lg;
  }
  
  /* الجداول */
  .table {
    @apply min-w-full divide-y divide-gray-200;
  }
  
  .table-header {
    @apply bg-gray-50;
  }
  
  .table-header-cell {
    @apply px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider;
  }
  
  .table-body {
    @apply bg-white divide-y divide-gray-200;
  }
  
  .table-row {
    @apply hover:bg-gray-50 transition-colors duration-150;
  }
  
  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
  }
  
  /* التنقل */
  .nav-link {
    @apply flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200;
  }
  
  .nav-link-active {
    @apply nav-link bg-primary-100 text-primary-700;
  }
  
  .nav-link-inactive {
    @apply nav-link text-gray-600 hover:text-gray-900 hover:bg-gray-100;
  }
  
  /* الشارات */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-success {
    @apply badge bg-success-100 text-success-800;
  }
  
  .badge-warning {
    @apply badge bg-warning-100 text-warning-800;
  }
  
  .badge-danger {
    @apply badge bg-danger-100 text-danger-800;
  }
  
  .badge-info {
    @apply badge bg-primary-100 text-primary-800;
  }
  
  /* التنبيهات */
  .alert {
    @apply p-4 rounded-lg border;
  }
  
  .alert-success {
    @apply alert bg-success-50 border-success-200 text-success-800;
  }
  
  .alert-warning {
    @apply alert bg-warning-50 border-warning-200 text-warning-800;
  }
  
  .alert-danger {
    @apply alert bg-danger-50 border-danger-200 text-danger-800;
  }
  
  .alert-info {
    @apply alert bg-primary-50 border-primary-200 text-primary-800;
  }
  
  /* الشريط الجانبي */
  .sidebar {
    @apply fixed right-0 top-0 h-full w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out;
  }
  
  .sidebar-open {
    @apply translate-x-0;
  }
  
  .sidebar-closed {
    @apply translate-x-full;
  }
  
  /* المحتوى الرئيسي */
  .main-content {
    @apply mr-64 transition-all duration-300 ease-in-out;
  }
  
  .main-content-full {
    @apply mr-0;
  }
  
  /* شريط الأدوات */
  .toolbar {
    @apply flex items-center justify-between p-4 bg-white border-b border-gray-200;
  }
  
  /* النماذج */
  .form-group {
    @apply mb-4;
  }
  
  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }
  
  .form-error {
    @apply mt-1 text-sm text-danger-600;
  }
  
  /* الأيقونات */
  .icon {
    @apply w-5 h-5;
  }
  
  .icon-sm {
    @apply w-4 h-4;
  }
  
  .icon-lg {
    @apply w-6 h-6;
  }
  
  /* الرسوم البيانية */
  .chart-container {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-4;
  }
  
  /* الطباعة */
  @media print {
    .no-print {
      display: none !important;
    }
    
    .print-only {
      display: block !important;
    }
  }
}

@layer utilities {
  /* اتجاه النص */
  .text-rtl {
    direction: rtl;
  }
  
  .text-ltr {
    direction: ltr;
  }
  
  /* الظلال المخصصة */
  .shadow-soft {
    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
  }
  
  /* التدرجات المخصصة */
  .bg-gradient-libya {
    background: linear-gradient(135deg, #DA2032 0%, #009639 100%);
  }
  
  /* الحركات */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  
  @keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
  }
}

/* === تصميم index-standalone.html === */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}
body {
    font-family: 'Cairo', sans-serif;
    direction: rtl;
    background: #f8fafc;
}
.sidebar {
    position: fixed;
    right: 0;
    top: 0;
    height: 100vh;
    width: 280px;
    background: white;
    box-shadow: -2px 0 10px rgba(0,0,0,0.1);
    z-index: 50;
    transition: transform 0.3s ease;
    display: flex;
    flex-direction: column;
}
.sidebar-header { flex-shrink: 0; }
.sidebar-nav { flex: 1; overflow-y: auto; padding: 1rem; }
.sidebar-nav::-webkit-scrollbar { width: 6px; }
.sidebar-nav::-webkit-scrollbar-track { background: #f1f5f9; border-radius: 3px; }
.sidebar-nav::-webkit-scrollbar-thumb { background: #cbd5e1; border-radius: 3px; }
.sidebar-nav::-webkit-scrollbar-thumb:hover { background: #94a3b8; }
.main-content { margin-right: 280px; transition: margin-right 0.3s ease; }
.main-content.full { margin-right: 0; }
@media (max-width: 1023px) {
    .sidebar { top: 48px; height: calc(100vh - 48px); }
    .main-content { margin-top: 48px; }
}
.card { background: white; border-radius: 8px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); border: 1px solid #e5e7eb; }
.card-header { padding: 1.5rem; border-bottom: 1px solid #e5e7eb; }
.card-body { padding: 1.5rem; }
.btn { display: inline-flex; align-items: center; justify-content: center; padding: 0.5rem 1rem; border-radius: 0.5rem; font-weight: 500; transition: all 0.2s; cursor: pointer; border: none; text-decoration: none; }
.btn-primary { background: #0ea5e9; color: white; }
.btn-primary:hover { background: #0284c7; }
.nav-link { display: flex; align-items: center; padding: 0.75rem 1rem; color: #6b7280; text-decoration: none; border-radius: 0.5rem; margin: 0.25rem 0.5rem; transition: all 0.2s; }
.nav-link:hover { background: #f3f4f6; color: #374151; }
.nav-link.active { background: #dbeafe; color: #1d4ed8; }
.nav-section { margin-bottom: 0.5rem; }
.nav-section-header { display: flex; align-items: center; justify-content: space-between; padding: 0.5rem 1rem; color: #6b7280; font-size: 0.75rem; font-weight: 600; cursor: pointer; border-radius: 0.5rem; margin: 0.25rem 0.5rem; transition: all 0.2s; }
.nav-section-header:hover { background: #f3f4f6; color: #374151; }
.nav-section-header .section-icon { transition: transform 0.2s; transform: rotate(-90deg); }
.nav-section-header.expanded .section-icon { transform: rotate(0deg); }
.nav-section-content { overflow: hidden; transition: max-height 0.3s ease; max-height: 500px; }
.nav-section-content.collapsed { max-height: 0; }
.nav-section-content .nav-link { margin-right: 1rem; margin-left: 0.5rem; } 