const { app, BrowserWindow, <PERSON>u, ipc<PERSON>ain, session } = require('electron');
const path = require('path');

// تحديد مسار قاعدة البيانات حسب وضع التشغيل
let dbPath;
if (process.env.NODE_ENV === 'development') {
  dbPath = path.join(__dirname, '..', 'prisma', 'almamry_erp.db');
} else {
  dbPath = path.join(process.resourcesPath, 'prisma', 'almamry_erp.db');
}
process.env.DATABASE_URL = `file:${dbPath}`;

const { PrismaClient } = require('@prisma/client');
const isDev = process.env.NODE_ENV === 'development';

let mainWindow;
let prisma;

// تهيئة قاعدة البيانات
async function initializeDatabase() {
  try {
    prisma = new PrismaClient();
    await prisma.$connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');
    
    // التحقق من وجود إعدادات النظام
    const settings = await prisma.systemSettings.findFirst();
    if (!settings) {
      // إنشاء إعدادات افتراضية
      await prisma.systemSettings.create({
        data: {
          companyName: 'شركة المعماري لصناعة الزجاج والألومنيوم',
          companyAddress: 'طرابلس، ليبيا',
          companyPhone: '+218-21-1234567',
          companyEmail: '<EMAIL>',
          currency: 'دينار ليبي',
          currencySymbol: 'ل.د',
        }
      });
      console.log('✅ تم إنشاء إعدادات النظام الافتراضية');
    }

    // التحقق من وجود مستخدم مدير
    const adminUser = await prisma.user.findFirst({
      where: { role: 'ADMIN' }
    });
    
    if (!adminUser) {
      // إنشاء مستخدم مدير افتراضي
      await prisma.user.create({
        data: {
          username: 'admin',
          password: 'admin123', // في الإنتاج يجب تشفير كلمة المرور
          fullName: 'مدير النظام',
          email: '<EMAIL>',
          role: 'ADMIN',
        }
      });
      console.log('✅ تم إنشاء مستخدم المدير الافتراضي');
    }

    return true;
  } catch (error) {
    console.error('❌ خطأ في تهيئة قاعدة البيانات:', error);
    return false;
  }
}

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, '../public/icon.ico'),
    title: 'Almamry ERP - نظام إدارة موارد المؤسسات',
    show: false,
    autoHideMenuBar: true
  });

  // تحميل التطبيق
  if (isDev) {
    mainWindow.loadURL('http://localhost:5173');
    mainWindow.webContents.openDevTools();
  } else {
    // في وضع الإنتاج، نحاول تحميل من dist أو نعود للتطوير
    const distPath = path.join(__dirname, '../dist/index.html');
    if (require('fs').existsSync(distPath)) {
      mainWindow.loadFile(distPath);
    } else {
      mainWindow.loadURL('http://localhost:5173');
      console.log('⚠️ ملف dist غير موجود، تم التحميل من وضع التطوير');
    }
  }

  // إظهار النافذة عند جاهزية المحتوى
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // إغلاق النافذة
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// إنشاء قائمة التطبيق
function createMenu() {
  const template = [
    {
      label: 'ملف',
      submenu: [
        {
          label: 'خروج',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'عرض',
      submenu: [
        { role: 'reload', label: 'إعادة تحميل' },
        { role: 'forceReload', label: 'إعادة تحميل قسري' },
        { role: 'toggleDevTools', label: 'أدوات المطور' },
        { type: 'separator' },
        { role: 'resetZoom', label: 'تكبير عادي' },
        { role: 'zoomIn', label: 'تكبير' },
        { role: 'zoomOut', label: 'تصغير' },
        { type: 'separator' },
        { role: 'togglefullscreen', label: 'ملء الشاشة' }
      ]
    },
    {
      label: 'مساعدة',
      submenu: [
        {
          label: 'حول Almamry ERP',
          click: () => {
            // إظهار معلومات التطبيق
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// أحداث التطبيق
app.whenReady().then(async () => {
  // تهيئة قاعدة البيانات أولاً
  const dbInitialized = await initializeDatabase();
  if (!dbInitialized) {
    console.error('❌ فشل في تهيئة قاعدة البيانات');
    app.quit();
    return;
  }

  createWindow();
  createMenu();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// منع فتح نوافذ متعددة
app.on('second-instance', () => {
  if (mainWindow) {
    if (mainWindow.isMinimized()) mainWindow.restore();
    mainWindow.focus();
  }
});

// IPC Handlers
ipcMain.handle('get-app-version', () => {
  return app.getVersion();
});

ipcMain.handle('get-app-name', () => {
  return app.getName();
});

// معالجات قاعدة البيانات
ipcMain.handle('db-get-customers', async () => {
  try {
    return await prisma.customer.findMany({
      orderBy: { name: 'asc' }
    });
  } catch (error) {
    console.error('خطأ في جلب العملاء:', error);
    throw error;
  }
});

ipcMain.handle('db-create-customer', async (event, customerData) => {
  try {
    return await prisma.customer.create({
      data: customerData
    });
  } catch (error) {
    console.error('خطأ في إنشاء العميل:', error);
    throw error;
  }
});

ipcMain.handle('db-get-suppliers', async () => {
  try {
    return await prisma.supplier.findMany({
      orderBy: { name: 'asc' }
    });
  } catch (error) {
    console.error('خطأ في جلب الموردين:', error);
    throw error;
  }
});

ipcMain.handle('db-create-supplier', async (event, supplierData) => {
  try {
    return await prisma.supplier.create({
      data: supplierData
    });
  } catch (error) {
    console.error('خطأ في إنشاء المورد:', error);
    throw error;
  }
});

ipcMain.handle('db-get-items', async () => {
  try {
    return await prisma.item.findMany({
      orderBy: { name: 'asc' }
    });
  } catch (error) {
    console.error('خطأ في جلب الأصناف:', error);
    throw error;
  }
});

ipcMain.handle('db-create-item', async (event, itemData) => {
  try {
    return await prisma.item.create({
      data: itemData
    });
  } catch (error) {
    console.error('خطأ في إنشاء الصنف:', error);
    throw error;
  }
});

ipcMain.handle('db-get-stats', async () => {
  try {
    const [
      customersCount,
      suppliersCount,
      itemsCount,
      salesInvoicesCount,
      purchaseInvoicesCount
    ] = await Promise.all([
      prisma.customer.count(),
      prisma.supplier.count(),
      prisma.item.count(),
      prisma.salesInvoice.count(),
      prisma.purchaseInvoice.count()
    ]);

    const totalSales = await prisma.salesInvoice.aggregate({
      _sum: { total: true }
    });

    return {
      customersCount,
      suppliersCount,
      itemsCount,
      salesInvoicesCount,
      purchaseInvoicesCount,
      totalSales: totalSales._sum.total || 0
    };
  } catch (error) {
    console.error('خطأ في جلب الإحصائيات:', error);
    throw error;
  }
});

// إغلاق قاعدة البيانات عند إغلاق التطبيق
app.on('before-quit', async () => {
  if (prisma) {
    await prisma.$disconnect();
    console.log('✅ تم إغلاق قاعدة البيانات');
  }
});

// كود الحماية: منع أي اتصال خارجي بالإنترنت
app.on('ready', () => {
  session.defaultSession.webRequest.onBeforeRequest((details, callback) => {
    const url = details.url;
    // اسمح فقط بالملفات المحلية و localhost
    if (
      url.startsWith('file://') ||
      url.startsWith('http://localhost') ||
      url.startsWith('http://127.0.0.1')
    ) {
      callback({ cancel: false });
    } else {
      console.log('🚫 تم منع محاولة اتصال خارجي:', url);
      callback({ cancel: true });
    }
  });
}); 