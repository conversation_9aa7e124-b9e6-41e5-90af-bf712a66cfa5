const { PrismaClient } = require('@prisma/client');
const path = require('path');
const fs = require('fs');

// تحديد مسار قاعدة البيانات
const dbPath = path.join(__dirname, '..', 'prisma', 'almamry_erp.db');
process.env.DATABASE_URL = `file:${dbPath}`;

const prisma = new PrismaClient();

async function initializeDatabase() {
  try {
    console.log('🔄 بدء تهيئة قاعدة البيانات...');
    
    // الاتصال بقاعدة البيانات
    await prisma.$connect();
    console.log('✅ تم الاتصال بقاعدة البيانات');

    // تشغيل migrations
    console.log('🔄 تطبيق migrations...');
    const { execSync } = require('child_process');
    try {
      execSync('npx prisma migrate deploy', { 
        stdio: 'inherit',
        cwd: path.join(__dirname, '..')
      });
      console.log('✅ تم تطبيق migrations بنجاح');
    } catch (error) {
      console.log('⚠️ تحذير: لم يتم تطبيق migrations، سيتم إنشاء الجداول يدوياً');
      await createTablesManually();
    }

    // إنشاء البيانات الأولية
    await createInitialData();
    
    console.log('✅ تم تهيئة قاعدة البيانات بنجاح');
    
  } catch (error) {
    console.error('❌ خطأ في تهيئة قاعدة البيانات:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

async function createTablesManually() {
  console.log('🔄 إنشاء الجداول يدوياً...');
  
  // إنشاء جدول SystemSettings
  await prisma.$executeRaw`
    CREATE TABLE IF NOT EXISTS "SystemSettings" (
      "id" TEXT NOT NULL PRIMARY KEY,
      "companyName" TEXT NOT NULL,
      "companyAddress" TEXT,
      "companyPhone" TEXT,
      "companyEmail" TEXT,
      "companyWebsite" TEXT,
      "taxNumber" TEXT,
      "currency" TEXT NOT NULL DEFAULT 'دينار ليبي',
      "currencySymbol" TEXT NOT NULL DEFAULT 'ل.د',
      "fiscalYearStart" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
      "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
      "updatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
    );
  `;

  // إنشاء جدول User
  await prisma.$executeRaw`
    CREATE TABLE IF NOT EXISTS "User" (
      "id" TEXT NOT NULL PRIMARY KEY,
      "username" TEXT NOT NULL UNIQUE,
      "password" TEXT NOT NULL,
      "fullName" TEXT NOT NULL,
      "name" TEXT NOT NULL,
      "email" TEXT UNIQUE,
      "role" TEXT NOT NULL DEFAULT 'USER',
      "isActive" BOOLEAN NOT NULL DEFAULT true,
      "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
      "updatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
    );
  `;

  // إنشاء جدول Customer
  await prisma.$executeRaw`
    CREATE TABLE IF NOT EXISTS "Customer" (
      "id" TEXT NOT NULL PRIMARY KEY,
      "code" TEXT NOT NULL UNIQUE,
      "name" TEXT NOT NULL,
      "phone" TEXT,
      "email" TEXT,
      "address" TEXT,
      "taxNumber" TEXT,
      "creditLimit" REAL,
      "balance" REAL NOT NULL DEFAULT 0,
      "isActive" BOOLEAN NOT NULL DEFAULT true,
      "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
      "updatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
    );
  `;

  // إنشاء جدول Supplier
  await prisma.$executeRaw`
    CREATE TABLE IF NOT EXISTS "Supplier" (
      "id" TEXT NOT NULL PRIMARY KEY,
      "code" TEXT NOT NULL UNIQUE,
      "name" TEXT NOT NULL,
      "phone" TEXT,
      "email" TEXT,
      "address" TEXT,
      "taxNumber" TEXT,
      "balance" REAL NOT NULL DEFAULT 0,
      "isActive" BOOLEAN NOT NULL DEFAULT true,
      "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
      "updatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
    );
  `;

  // إنشاء جدول Item
  await prisma.$executeRaw`
    CREATE TABLE IF NOT EXISTS "Item" (
      "id" TEXT NOT NULL PRIMARY KEY,
      "code" TEXT NOT NULL UNIQUE,
      "name" TEXT NOT NULL,
      "description" TEXT,
      "category" TEXT,
      "unit" TEXT NOT NULL,
      "costPrice" REAL NOT NULL DEFAULT 0,
      "sellingPrice" REAL NOT NULL DEFAULT 0,
      "minQuantity" INTEGER NOT NULL DEFAULT 0,
      "maxQuantity" INTEGER NOT NULL DEFAULT 0,
      "currentQuantity" INTEGER NOT NULL DEFAULT 0,
      "isActive" BOOLEAN NOT NULL DEFAULT true,
      "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
      "updatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
    );
  `;

  console.log('✅ تم إنشاء الجداول الأساسية');
}

async function createInitialData() {
  console.log('🔄 إنشاء البيانات الأولية...');
  
  // التحقق من وجود إعدادات النظام
  const existingSettings = await prisma.systemSettings.findFirst();
  if (!existingSettings) {
    await prisma.systemSettings.create({
      data: {
        companyName: 'شركة المعماري لصناعة الزجاج والألومنيوم',
        companyAddress: 'طرابلس، ليبيا',
        companyPhone: '+218-21-1234567',
        companyEmail: '<EMAIL>',
        currency: 'دينار ليبي',
        currencySymbol: 'ل.د',
      }
    });
    console.log('✅ تم إنشاء إعدادات النظام');
  }

  // التحقق من وجود مستخدم مدير
  const existingAdmin = await prisma.user.findFirst({
    where: { role: 'ADMIN' }
  });
  
  if (!existingAdmin) {
    await prisma.user.create({
      data: {
        username: 'admin',
        password: 'admin123',
        fullName: 'مدير النظام',
        name: 'مدير النظام',
        email: '<EMAIL>',
        role: 'ADMIN',
      }
    });
    console.log('✅ تم إنشاء مستخدم المدير');
  }

  // إنشاء بيانات تجريبية
  await createSampleData();
}

async function createSampleData() {
  console.log('🔄 إنشاء بيانات تجريبية...');
  
  // إنشاء عملاء تجريبيين
  const existingCustomers = await prisma.customer.count();
  if (existingCustomers === 0) {
    await prisma.customer.createMany({
      data: [
        {
          code: 'C001',
          name: 'شركة البناء الحديث',
          phone: '+218-21-1111111',
          email: '<EMAIL>',
          address: 'طرابلس، ليبيا',
          creditLimit: 50000,
          balance: 0
        },
        {
          code: 'C002', 
          name: 'مؤسسة الإنشاءات المتطورة',
          phone: '+218-21-2222222',
          email: '<EMAIL>',
          address: 'بنغازي، ليبيا',
          creditLimit: 75000,
          balance: 0
        }
      ]
    });
    console.log('✅ تم إنشاء العملاء التجريبيين');
  }

  // إنشاء موردين تجريبيين
  const existingSuppliers = await prisma.supplier.count();
  if (existingSuppliers === 0) {
    await prisma.supplier.createMany({
      data: [
        {
          code: 'S001',
          name: 'مصنع الألومنيوم الليبي',
          phone: '+218-21-3333333',
          email: '<EMAIL>',
          address: 'مصراتة، ليبيا',
          balance: 0
        },
        {
          code: 'S002',
          name: 'شركة الزجاج المتخصص',
          phone: '+218-21-4444444',
          email: '<EMAIL>',
          address: 'طرابلس، ليبيا',
          balance: 0
        }
      ]
    });
    console.log('✅ تم إنشاء الموردين التجريبيين');
  }

  // إنشاء أصناف تجريبية
  const existingItems = await prisma.item.count();
  if (existingItems === 0) {
    await prisma.item.createMany({
      data: [
        {
          code: 'I001',
          name: 'زجاج شفاف 6مم',
          description: 'زجاج شفاف عالي الجودة سماكة 6مم',
          category: 'زجاج',
          unit: 'متر مربع',
          costPrice: 25.0,
          sellingPrice: 35.0,
          minQuantity: 10,
          maxQuantity: 1000,
          currentQuantity: 100
        },
        {
          code: 'I002',
          name: 'ألومنيوم أبيض',
          description: 'ألومنيوم أبيض للنوافذ والأبواب',
          category: 'ألومنيوم',
          unit: 'متر طولي',
          costPrice: 15.0,
          sellingPrice: 22.0,
          minQuantity: 50,
          maxQuantity: 2000,
          currentQuantity: 500
        }
      ]
    });
    console.log('✅ تم إنشاء الأصناف التجريبية');
  }
}

// تشغيل السكريبت
if (require.main === module) {
  initializeDatabase()
    .then(() => {
      console.log('🎉 تم تهيئة قاعدة البيانات بنجاح!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ فشل في تهيئة قاعدة البيانات:', error);
      process.exit(1);
    });
}

module.exports = { initializeDatabase, createInitialData };
