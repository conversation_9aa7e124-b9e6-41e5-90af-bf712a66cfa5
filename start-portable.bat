@echo off
REM سكريبت شامل لتجهيز النسخة المحمولة في مجلد واحد وتشغيلها تلقائيًا

REM اسم المجلد النهائي
set TARGET_DIR=PortableERP
REM اسم ملف EXE النهائي
set EXE_NAME=Almamry ERP.exe
REM مسار مجلد البناء
set BUILD_DIR=dist\win-unpacked

REM حذف المجلد القديم إذا كان موجود
if exist "%TARGET_DIR%" rmdir /s /q "%TARGET_DIR%"

REM إنشاء المجلد الجديد
mkdir "%TARGET_DIR%"

REM نسخ كل الملفات والمجلدات المطلوبة
xcopy "%BUILD_DIR%\*" "%TARGET_DIR%\" /E /H /C /I /Y

REM نسخ قاعدة البيانات إذا كانت موجودة في مجلد prisma
if exist prisma\almamry_erp.db copy /Y prisma\almamry_erp.db "%TARGET_DIR%\prisma\almamry_erp.db"

REM إعادة تهيئة القاعدة وتشغيل seed (اختياري)
REM يمكنك إزالة السطرين التاليين إذا لم ترغب في إعادة تهيئة القاعدة كل مرة
call npx prisma migrate reset --force
call npx prisma db seed

REM تشغيل البرنامج تلقائيًا
cd "%TARGET_DIR%"
start "" "%EXE_NAME%"
cd ..

echo تم تجهيز النسخة المحمولة وتشغيلها بنجاح.
pause 