const { app, BrowserWindow, <PERSON>u, ipcMain, session } = require('electron');
const path = require('path');
const fs = require('fs');

// تحديد مسار قاعدة البيانات حسب وضع التشغيل
let dbPath;
const isDev = process.env.NODE_ENV === 'development' || !app.isPackaged;

if (isDev) {
  dbPath = path.join(__dirname, '..', 'prisma', 'almamry_erp.db');
  // إذا لم تكن قاعدة البيانات موجودة، انسخ النسخة المهيأة
  if (!fs.existsSync(dbPath)) {
    const seedDbPath = path.join(__dirname, '..', 'prisma', 'almamry_erp_electron.db');
    if (fs.existsSync(seedDbPath)) {
      fs.copyFileSync(seedDbPath, dbPath);
      console.log('✅ تم نسخ قاعدة البيانات المهيأة');
    }
  }
} else {
  dbPath = path.join(process.resourcesPath, 'prisma', 'almamry_erp.db');
}

// التأكد من وجود مجلد prisma
const prismaDir = path.dirname(dbPath);
if (!fs.existsSync(prismaDir)) {
  fs.mkdirSync(prismaDir, { recursive: true });
}

process.env.DATABASE_URL = `file:${dbPath}`;
console.log('📍 مسار قاعدة البيانات:', dbPath);

const { PrismaClient } = require('@prisma/client');

let mainWindow;
let prisma;

// دالة للتحقق من وجود الجداول وإنشاؤها
async function ensureTablesExist() {
  try {
    // التحقق من وجود جدول SystemSettings
    await prisma.$queryRaw`SELECT name FROM sqlite_master WHERE type='table' AND name='SystemSettings';`;
    console.log('✅ الجداول موجودة');
  } catch (error) {
    console.log('⚠️ الجداول غير موجودة، سيتم إنشاؤها...');
    await createDatabaseFromScratch();
  }
}

// دالة لإنشاء قاعدة البيانات من الصفر
async function createDatabaseFromScratch() {
  console.log('🔄 إنشاء قاعدة البيانات من الصفر...');

  // إنشاء جدول SystemSettings
  await prisma.$executeRaw`
    CREATE TABLE IF NOT EXISTS "SystemSettings" (
      "id" TEXT NOT NULL PRIMARY KEY,
      "companyName" TEXT NOT NULL,
      "companyAddress" TEXT,
      "companyPhone" TEXT,
      "companyEmail" TEXT,
      "companyWebsite" TEXT,
      "taxNumber" TEXT,
      "currency" TEXT NOT NULL DEFAULT 'دينار ليبي',
      "currencySymbol" TEXT NOT NULL DEFAULT 'ل.د',
      "fiscalYearStart" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
      "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
      "updatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
    );
  `;

  // إنشاء جدول User
  await prisma.$executeRaw`
    CREATE TABLE IF NOT EXISTS "User" (
      "id" TEXT NOT NULL PRIMARY KEY,
      "username" TEXT NOT NULL UNIQUE,
      "password" TEXT NOT NULL,
      "fullName" TEXT NOT NULL,
      "name" TEXT NOT NULL,
      "email" TEXT UNIQUE,
      "role" TEXT NOT NULL DEFAULT 'USER',
      "isActive" BOOLEAN NOT NULL DEFAULT true,
      "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
      "updatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
    );
  `;

  // إنشاء جدول Customer
  await prisma.$executeRaw`
    CREATE TABLE IF NOT EXISTS "Customer" (
      "id" TEXT NOT NULL PRIMARY KEY,
      "code" TEXT NOT NULL UNIQUE,
      "name" TEXT NOT NULL,
      "phone" TEXT,
      "email" TEXT,
      "address" TEXT,
      "taxNumber" TEXT,
      "creditLimit" REAL,
      "balance" REAL NOT NULL DEFAULT 0,
      "isActive" BOOLEAN NOT NULL DEFAULT true,
      "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
      "updatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
    );
  `;

  // إنشاء جدول Supplier
  await prisma.$executeRaw`
    CREATE TABLE IF NOT EXISTS "Supplier" (
      "id" TEXT NOT NULL PRIMARY KEY,
      "code" TEXT NOT NULL UNIQUE,
      "name" TEXT NOT NULL,
      "phone" TEXT,
      "email" TEXT,
      "address" TEXT,
      "taxNumber" TEXT,
      "balance" REAL NOT NULL DEFAULT 0,
      "isActive" BOOLEAN NOT NULL DEFAULT true,
      "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
      "updatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
    );
  `;

  // إنشاء جدول Item
  await prisma.$executeRaw`
    CREATE TABLE IF NOT EXISTS "Item" (
      "id" TEXT NOT NULL PRIMARY KEY,
      "code" TEXT NOT NULL UNIQUE,
      "name" TEXT NOT NULL,
      "description" TEXT,
      "category" TEXT,
      "unit" TEXT NOT NULL,
      "costPrice" REAL NOT NULL DEFAULT 0,
      "sellingPrice" REAL NOT NULL DEFAULT 0,
      "minQuantity" INTEGER NOT NULL DEFAULT 0,
      "maxQuantity" INTEGER NOT NULL DEFAULT 0,
      "currentQuantity" INTEGER NOT NULL DEFAULT 0,
      "isActive" BOOLEAN NOT NULL DEFAULT true,
      "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
      "updatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
    );
  `;

  // إنشاء جدول SalesInvoice
  await prisma.$executeRaw`
    CREATE TABLE IF NOT EXISTS "SalesInvoice" (
      "id" TEXT NOT NULL PRIMARY KEY,
      "invoiceNumber" TEXT NOT NULL UNIQUE,
      "invoiceDate" DATETIME NOT NULL,
      "dueDate" DATETIME,
      "customerId" TEXT NOT NULL,
      "userId" TEXT NOT NULL,
      "subtotal" REAL NOT NULL DEFAULT 0,
      "taxAmount" REAL NOT NULL DEFAULT 0,
      "discount" REAL NOT NULL DEFAULT 0,
      "total" REAL NOT NULL DEFAULT 0,
      "paidAmount" REAL NOT NULL DEFAULT 0,
      "status" TEXT NOT NULL DEFAULT 'DRAFT',
      "notes" TEXT,
      "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
      "updatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY ("customerId") REFERENCES "Customer" ("id"),
      FOREIGN KEY ("userId") REFERENCES "User" ("id")
    );
  `;

  // إنشاء جدول PurchaseInvoice
  await prisma.$executeRaw`
    CREATE TABLE IF NOT EXISTS "PurchaseInvoice" (
      "id" TEXT NOT NULL PRIMARY KEY,
      "invoiceNumber" TEXT NOT NULL UNIQUE,
      "invoiceDate" DATETIME NOT NULL,
      "dueDate" DATETIME,
      "supplierId" TEXT NOT NULL,
      "userId" TEXT NOT NULL,
      "subtotal" REAL NOT NULL DEFAULT 0,
      "taxAmount" REAL NOT NULL DEFAULT 0,
      "discount" REAL NOT NULL DEFAULT 0,
      "total" REAL NOT NULL DEFAULT 0,
      "paidAmount" REAL NOT NULL DEFAULT 0,
      "status" TEXT NOT NULL DEFAULT 'DRAFT',
      "notes" TEXT,
      "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
      "updatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY ("supplierId") REFERENCES "Supplier" ("id"),
      FOREIGN KEY ("userId") REFERENCES "User" ("id")
    );
  `;

  console.log('✅ تم إنشاء جميع الجداول الأساسية');
}

// تهيئة قاعدة البيانات
async function initializeDatabase() {
  try {
    prisma = new PrismaClient();
    await prisma.$connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // تشغيل migrations إذا لم تكن مطبقة
    try {
      await prisma.$executeRaw`PRAGMA foreign_keys = ON;`;
      console.log('✅ تم تفعيل foreign keys');
    } catch (error) {
      console.log('⚠️ تحذير: لم يتم تفعيل foreign keys');
    }

    // التحقق من وجود الجداول وإنشاؤها إذا لم تكن موجودة
    await ensureTablesExist();

    // التحقق من وجود إعدادات النظام
    const settings = await prisma.systemSettings.findFirst();
    if (!settings) {
      // إنشاء إعدادات افتراضية
      await prisma.systemSettings.create({
        data: {
          companyName: 'شركة المعماري لصناعة الزجاج والألومنيوم',
          companyAddress: 'طرابلس، ليبيا',
          companyPhone: '+218-21-1234567',
          companyEmail: '<EMAIL>',
          currency: 'دينار ليبي',
          currencySymbol: 'ل.د',
        }
      });
      console.log('✅ تم إنشاء إعدادات النظام الافتراضية');
    }

    // التحقق من وجود مستخدم مدير
    const adminUser = await prisma.user.findFirst({
      where: { role: 'ADMIN' }
    });

    if (!adminUser) {
      // إنشاء مستخدم مدير افتراضي
      await prisma.user.create({
        data: {
          username: 'admin',
          password: 'admin123', // في الإنتاج يجب تشفير كلمة المرور
          fullName: 'مدير النظام',
          name: 'مدير النظام', // إضافة الحقل المطلوب
          email: '<EMAIL>',
          role: 'ADMIN',
        }
      });
      console.log('✅ تم إنشاء مستخدم المدير الافتراضي');
    }

    return true;
  } catch (error) {
    console.error('❌ خطأ في تهيئة قاعدة البيانات:', error);

    // محاولة إنشاء قاعدة البيانات من الصفر
    try {
      console.log('🔄 محاولة إنشاء قاعدة البيانات من الصفر...');
      await createDatabaseFromScratch();
      return await initializeDatabase(); // إعادة المحاولة
    } catch (createError) {
      console.error('❌ فشل في إنشاء قاعدة البيانات:', createError);
      return false;
    }
  }
}

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, '../public/icon.ico'),
    title: 'Almamry ERP - نظام إدارة موارد المؤسسات',
    show: false,
    autoHideMenuBar: true
  });

  // تحميل التطبيق
  if (isDev) {
    mainWindow.loadURL('http://localhost:5173');
    mainWindow.webContents.openDevTools();
  } else {
    // في وضع الإنتاج، نحاول تحميل من dist أو نعود للتطوير
    const distPath = path.join(__dirname, '../dist/index.html');
    if (require('fs').existsSync(distPath)) {
      mainWindow.loadFile(distPath);
    } else {
      // إنشاء صفحة خطأ مؤقتة
      createErrorPage();
    }
  }

  // إظهار النافذة عند جاهزية المحتوى
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // معالجة أخطاء التحميل
  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
    console.error('❌ فشل في تحميل التطبيق:', errorDescription);
    createErrorPage();
  });

  // إغلاق النافذة
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// إنشاء صفحة خطأ
function createErrorPage() {
  const errorHtml = `
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>خطأ في التحميل - Almamry ERP</title>
      <style>
        body {
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          margin: 0;
          padding: 20px;
          min-height: 100vh;
          display: flex;
          align-items: center;
          justify-content: center;
          direction: rtl;
        }
        .error-container {
          text-align: center;
          max-width: 600px;
          background: rgba(255, 255, 255, 0.1);
          padding: 40px;
          border-radius: 15px;
          backdrop-filter: blur(10px);
        }
        h1 { font-size: 2.5em; margin-bottom: 20px; }
        p { font-size: 1.2em; line-height: 1.6; margin-bottom: 15px; }
        .solutions { text-align: right; margin-top: 30px; }
        .solutions h3 { color: #ffd700; }
        .solutions li { margin: 10px 0; }
        button {
          background: #4CAF50;
          color: white;
          border: none;
          padding: 15px 30px;
          font-size: 16px;
          border-radius: 5px;
          cursor: pointer;
          margin: 10px;
        }
        button:hover { background: #45a049; }
      </style>
    </head>
    <body>
      <div class="error-container">
        <h1>⚠️ خطأ في تحميل التطبيق</h1>
        <p>عذراً، حدث خطأ في تحميل نظام Almamry ERP</p>

        <div class="solutions">
          <h3>الحلول المقترحة:</h3>
          <ul>
            <li>تأكد من تثبيت Node.js على النظام</li>
            <li>تأكد من وجود ملفات التطبيق كاملة</li>
            <li>جرب إعادة تشغيل التطبيق</li>
            <li>تحقق من أن قاعدة البيانات مهيأة بشكل صحيح</li>
          </ul>
        </div>

        <button onclick="location.reload()">إعادة المحاولة</button>
        <button onclick="require('electron').ipcRenderer.send('restart-app')">إعادة تشغيل التطبيق</button>
      </div>
    </body>
    </html>
  `;

  mainWindow.loadURL('data:text/html;charset=utf-8,' + encodeURIComponent(errorHtml));
}

// إنشاء قائمة التطبيق
function createMenu() {
  const template = [
    {
      label: 'ملف',
      submenu: [
        {
          label: 'خروج',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'عرض',
      submenu: [
        { role: 'reload', label: 'إعادة تحميل' },
        { role: 'forceReload', label: 'إعادة تحميل قسري' },
        { role: 'toggleDevTools', label: 'أدوات المطور' },
        { type: 'separator' },
        { role: 'resetZoom', label: 'تكبير عادي' },
        { role: 'zoomIn', label: 'تكبير' },
        { role: 'zoomOut', label: 'تصغير' },
        { type: 'separator' },
        { role: 'togglefullscreen', label: 'ملء الشاشة' }
      ]
    },
    {
      label: 'مساعدة',
      submenu: [
        {
          label: 'حول Almamry ERP',
          click: () => {
            // إظهار معلومات التطبيق
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// أحداث التطبيق
app.whenReady().then(async () => {
  // تهيئة قاعدة البيانات أولاً
  const dbInitialized = await initializeDatabase();
  if (!dbInitialized) {
    console.error('❌ فشل في تهيئة قاعدة البيانات');
    app.quit();
    return;
  }

  createWindow();
  createMenu();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// منع فتح نوافذ متعددة
app.on('second-instance', () => {
  if (mainWindow) {
    if (mainWindow.isMinimized()) mainWindow.restore();
    mainWindow.focus();
  }
});

// IPC Handlers
ipcMain.handle('get-app-version', () => {
  return app.getVersion();
});

ipcMain.handle('get-app-name', () => {
  return app.getName();
});

// معالج إعادة تشغيل التطبيق
ipcMain.on('restart-app', () => {
  app.relaunch();
  app.exit();
});

// معالجات قاعدة البيانات
ipcMain.handle('db-get-customers', async () => {
  try {
    return await prisma.customer.findMany({
      orderBy: { name: 'asc' }
    });
  } catch (error) {
    console.error('خطأ في جلب العملاء:', error);
    throw error;
  }
});

ipcMain.handle('db-create-customer', async (event, customerData) => {
  try {
    return await prisma.customer.create({
      data: customerData
    });
  } catch (error) {
    console.error('خطأ في إنشاء العميل:', error);
    throw error;
  }
});

ipcMain.handle('db-get-suppliers', async () => {
  try {
    return await prisma.supplier.findMany({
      orderBy: { name: 'asc' }
    });
  } catch (error) {
    console.error('خطأ في جلب الموردين:', error);
    throw error;
  }
});

ipcMain.handle('db-create-supplier', async (event, supplierData) => {
  try {
    return await prisma.supplier.create({
      data: supplierData
    });
  } catch (error) {
    console.error('خطأ في إنشاء المورد:', error);
    throw error;
  }
});

ipcMain.handle('db-get-items', async () => {
  try {
    return await prisma.item.findMany({
      orderBy: { name: 'asc' }
    });
  } catch (error) {
    console.error('خطأ في جلب الأصناف:', error);
    throw error;
  }
});

ipcMain.handle('db-create-item', async (event, itemData) => {
  try {
    return await prisma.item.create({
      data: itemData
    });
  } catch (error) {
    console.error('خطأ في إنشاء الصنف:', error);
    throw error;
  }
});

ipcMain.handle('db-get-stats', async () => {
  try {
    const [
      customersCount,
      suppliersCount,
      itemsCount,
      salesInvoicesCount,
      purchaseInvoicesCount
    ] = await Promise.all([
      prisma.customer.count(),
      prisma.supplier.count(),
      prisma.item.count(),
      prisma.salesInvoice.count(),
      prisma.purchaseInvoice.count()
    ]);

    const totalSales = await prisma.salesInvoice.aggregate({
      _sum: { total: true }
    });

    return {
      customersCount,
      suppliersCount,
      itemsCount,
      salesInvoicesCount,
      purchaseInvoicesCount,
      totalSales: totalSales._sum.total || 0
    };
  } catch (error) {
    console.error('خطأ في جلب الإحصائيات:', error);
    throw error;
  }
});

// إغلاق قاعدة البيانات عند إغلاق التطبيق
app.on('before-quit', async () => {
  if (prisma) {
    await prisma.$disconnect();
    console.log('✅ تم إغلاق قاعدة البيانات');
  }
});

// كود الحماية: منع أي اتصال خارجي بالإنترنت
app.on('ready', () => {
  session.defaultSession.webRequest.onBeforeRequest((details, callback) => {
    const url = details.url;
    // اسمح فقط بالملفات المحلية و localhost
    if (
      url.startsWith('file://') ||
      url.startsWith('http://localhost') ||
      url.startsWith('http://127.0.0.1')
    ) {
      callback({ cancel: false });
    } else {
      console.log('🚫 تم منع محاولة اتصال خارجي:', url);
      callback({ cancel: true });
    }
  });
}); 