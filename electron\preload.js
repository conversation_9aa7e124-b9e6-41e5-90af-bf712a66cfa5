const { contextBridge, ipcRenderer } = require('electron');

// تعريض APIs آمنة للـ renderer process
contextBridge.exposeInMainWorld('electronAPI', {
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),
  getAppName: () => ipcRenderer.invoke('get-app-name'),
  
  // إدارة الملفات
  saveFile: (data) => ipcRenderer.invoke('save-file', data),
  openFile: () => ipcRenderer.invoke('open-file'),
  
  // الطباعة
  print: (data) => ipcRenderer.invoke('print', data),
  
  // إشعارات
  showNotification: (title, body) => ipcRenderer.invoke('show-notification', title, body),
  
  // إعدادات النظام
  getSystemInfo: () => ipcRenderer.invoke('get-system-info'),
  
  // قاعدة البيانات - واجهة جديدة
  database: {
    // العملاء
    getCustomers: () => ipcRenderer.invoke('db-get-customers'),
    createCustomer: (data) => ipcRenderer.invoke('db-create-customer', data),
    updateCustomer: (id, data) => ipcRenderer.invoke('db-update-customer', id, data),
    deleteCustomer: (id) => ipcRenderer.invoke('db-delete-customer', id),
    
    // الموردين
    getSuppliers: () => ipcRenderer.invoke('db-get-suppliers'),
    createSupplier: (data) => ipcRenderer.invoke('db-create-supplier', data),
    updateSupplier: (id, data) => ipcRenderer.invoke('db-update-supplier', id, data),
    deleteSupplier: (id) => ipcRenderer.invoke('db-delete-supplier', id),
    
    // الأصناف
    getItems: () => ipcRenderer.invoke('db-get-items'),
    createItem: (data) => ipcRenderer.invoke('db-create-item', data),
    updateItem: (id, data) => ipcRenderer.invoke('db-update-item', id, data),
    deleteItem: (id) => ipcRenderer.invoke('db-delete-item', id),
    
    // فواتير المبيعات
    getSalesInvoices: () => ipcRenderer.invoke('db-get-sales-invoices'),
    createSalesInvoice: (data) => ipcRenderer.invoke('db-create-sales-invoice', data),
    
    // فواتير المشتريات
    getPurchaseInvoices: () => ipcRenderer.invoke('db-get-purchase-invoices'),
    createPurchaseInvoice: (data) => ipcRenderer.invoke('db-create-purchase-invoice', data),
    
    // الإحصائيات
    getStats: () => ipcRenderer.invoke('db-get-stats'),
    
    // النسخ الاحتياطي والاستعادة
    backup: () => ipcRenderer.invoke('db-backup'),
    restore: (filePath) => ipcRenderer.invoke('db-restore', filePath),
  }
});

// إعدادات الأمان
window.addEventListener('DOMContentLoaded', () => {
  const replaceText = (selector, text) => {
    const element = document.getElementById(selector);
    if (element) element.innerText = text;
  };

  for (const dependency of ['chrome', 'node', 'electron']) {
    replaceText(`${dependency}-version`, process.versions[dependency]);
  }
}); 