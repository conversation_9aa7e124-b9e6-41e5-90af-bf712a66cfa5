import { create } from 'zustand';
import {  
  customerService, 
  supplierService, 
  itemService, 
  salesInvoiceService, 
  purchaseInvoiceService, 
  getGeneralStats,
  getSalesStats
} from '../services/api';

// أنواع البيانات
interface Customer {
  id: string;
  code: string;
  name: string;
  phone?: string;
  email?: string;
  address?: string;
  taxNumber?: string;
  creditLimit?: number;
  balance: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface Supplier {
  id: string;
  code: string;
  name: string;
  phone?: string;
  email?: string;
  address?: string;
  taxNumber?: string;
  balance: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface Item {
  id: string;
  code: string;
  name: string;
  description?: string;
  category?: string;
  unit: string;
  costPrice: number;
  sellingPrice: number;
  minQuantity: number;
  maxQuantity: number;
  currentQuantity: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface SalesInvoice {
  id: string;
  invoiceNumber: string;
  invoiceDate: Date;
  dueDate?: Date;
  customerId: string;
  userId: string;
  subtotal: number;
  taxAmount: number;
  discount: number;
  total: number;
  paidAmount: number;
  status: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
  customer?: Customer;
  user?: { id: string; fullName: string };
  items?: any[];
}

interface PurchaseInvoice {
  id: string;
  invoiceNumber: string;
  invoiceDate: Date;
  dueDate?: Date;
  supplierId: string;
  userId: string;
  subtotal: number;
  taxAmount: number;
  discount: number;
  total: number;
  paidAmount: number;
  status: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
  supplier?: Supplier;
  user?: { id: string; fullName: string };
  items?: any[];
}

interface Stats {
  customersCount: number;
  suppliersCount: number;
  itemsCount: number;
  salesInvoicesCount: number;
  purchaseInvoicesCount: number;
  totalSales: number;
  monthlySales: any[];
}

// حالة المتجر
interface DataState {
  // البيانات
  customers: Customer[];
  suppliers: Supplier[];
  items: Item[];
  salesInvoices: SalesInvoice[];
  purchaseInvoices: PurchaseInvoice[];
  stats: Stats;
  
  // حالة التحميل
  loading: {
    customers: boolean;
    suppliers: boolean;
    items: boolean;
    salesInvoices: boolean;
    purchaseInvoices: boolean;
    stats: boolean;
  };
  
  // الأخطاء
  errors: {
    customers?: string;
    suppliers?: string;
    items?: string;
    salesInvoices?: string;
    purchaseInvoices?: string;
    stats?: string;
  };
  
  // الإجراءات
  actions: {
    // العملاء
    loadCustomers: () => Promise<void>;
    createCustomer: (data: Omit<Customer, 'id' | 'createdAt' | 'updatedAt' | 'balance'>) => Promise<void>;
    updateCustomer: (id: string, data: Partial<Customer>) => Promise<void>;
    deleteCustomer: (id: string) => Promise<void>;
    
    // الموردين
    loadSuppliers: () => Promise<void>;
    createSupplier: (data: Omit<Supplier, 'id' | 'createdAt' | 'updatedAt' | 'balance'>) => Promise<void>;
    updateSupplier: (id: string, data: Partial<Supplier>) => Promise<void>;
    deleteSupplier: (id: string) => Promise<void>;
    
    // الأصناف
    loadItems: () => Promise<void>;
    createItem: (data: Omit<Item, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
    updateItem: (id: string, data: Partial<Item>) => Promise<void>;
    deleteItem: (id: string) => Promise<void>;
    
    // فواتير المبيعات
    loadSalesInvoices: () => Promise<void>;
    createSalesInvoice: (data: any) => Promise<void>;
    
    // فواتير المشتريات
    loadPurchaseInvoices: () => Promise<void>;
    createPurchaseInvoice: (data: any) => Promise<void>;
    
    // الإحصائيات
    loadStats: () => Promise<void>;
    
    // تحميل جميع البيانات
    loadAllData: () => Promise<void>;
  };
}

// إنشاء المتجر
export const useDataStore = create<DataState>((set, get) => ({
  // البيانات الأولية
  customers: [],
  suppliers: [],
  items: [],
  salesInvoices: [],
  purchaseInvoices: [],
  stats: {
    customersCount: 0,
    suppliersCount: 0,
    itemsCount: 0,
    salesInvoicesCount: 0,
    purchaseInvoicesCount: 0,
    totalSales: 0,
    monthlySales: []
  },
  
  // حالة التحميل
  loading: {
    customers: false,
    suppliers: false,
    items: false,
    salesInvoices: false,
    purchaseInvoices: false,
    stats: false
  },
  
  // الأخطاء
  errors: {},
  
  // الإجراءات
  actions: {
    // العملاء
    loadCustomers: async () => {
      set(state => ({ 
        loading: { ...state.loading, customers: true },
        errors: { ...state.errors, customers: undefined }
      }));
      
      try {
        const customers = await customerService.getAllCustomers();
        set(state => ({ 
          customers,
          loading: { ...state.loading, customers: false }
        }));
      } catch (error) {
        set(state => ({ 
          loading: { ...state.loading, customers: false },
          errors: { ...state.errors, customers: 'خطأ في تحميل العملاء' }
        }));
      }
    },
    
    createCustomer: async (data) => {
      try {
        const newCustomer = await customerService.createCustomer(data);
        set(state => ({ 
          customers: [...state.customers, newCustomer]
        }));
      } catch (error) {
        set(state => ({ 
          errors: { ...state.errors, customers: 'خطأ في إنشاء العميل' }
        }));
      }
    },
    
    updateCustomer: async (id, data) => {
      try {
        const updatedCustomer = await customerService.updateCustomer(id, data);
        set(state => ({ 
          customers: state.customers.map(c => c.id === id ? updatedCustomer : c)
        }));
      } catch (error) {
        set(state => ({ 
          errors: { ...state.errors, customers: 'خطأ في تحديث العميل' }
        }));
      }
    },
    
    deleteCustomer: async (id) => {
      try {
        await customerService.deleteCustomer(id);
        set(state => ({ 
          customers: state.customers.filter(c => c.id !== id)
        }));
      } catch (error) {
        set(state => ({ 
          errors: { ...state.errors, customers: 'خطأ في حذف العميل' }
        }));
      }
    },
    
    // الموردين
    loadSuppliers: async () => {
      set(state => ({ 
        loading: { ...state.loading, suppliers: true },
        errors: { ...state.errors, suppliers: undefined }
      }));
      
      try {
        const suppliers = await supplierService.getAllSuppliers();
        set(state => ({ 
          suppliers,
          loading: { ...state.loading, suppliers: false }
        }));
      } catch (error) {
        set(state => ({ 
          loading: { ...state.loading, suppliers: false },
          errors: { ...state.errors, suppliers: 'خطأ في تحميل الموردين' }
        }));
      }
    },
    
    createSupplier: async (data) => {
      try {
        const newSupplier = await supplierService.createSupplier(data);
        set(state => ({ 
          suppliers: [...state.suppliers, newSupplier]
        }));
      } catch (error) {
        set(state => ({ 
          errors: { ...state.errors, suppliers: 'خطأ في إنشاء المورد' }
        }));
      }
    },
    
    updateSupplier: async (id, data) => {
      try {
        const updatedSupplier = await supplierService.updateSupplier(id, data);
        set(state => ({ 
          suppliers: state.suppliers.map(s => s.id === id ? updatedSupplier : s)
        }));
      } catch (error) {
        set(state => ({ 
          errors: { ...state.errors, suppliers: 'خطأ في تحديث المورد' }
        }));
      }
    },
    
    deleteSupplier: async (id) => {
      try {
        await supplierService.deleteSupplier(id);
        set(state => ({ 
          suppliers: state.suppliers.filter(s => s.id !== id)
        }));
      } catch (error) {
        set(state => ({ 
          errors: { ...state.errors, suppliers: 'خطأ في حذف المورد' }
        }));
      }
    },
    
    // الأصناف
    loadItems: async () => {
      set(state => ({ 
        loading: { ...state.loading, items: true },
        errors: { ...state.errors, items: undefined }
      }));
      
      try {
        const items = await itemService.getAllItems();
        set(state => ({ 
          items,
          loading: { ...state.loading, items: false }
        }));
      } catch (error) {
        set(state => ({ 
          loading: { ...state.loading, items: false },
          errors: { ...state.errors, items: 'خطأ في تحميل الأصناف' }
        }));
      }
    },
    
    createItem: async (data) => {
      try {
        const newItem = await itemService.createItem(data);
        set(state => ({ 
          items: [...state.items, newItem]
        }));
      } catch (error) {
        set(state => ({ 
          errors: { ...state.errors, items: 'خطأ في إنشاء الصنف' }
        }));
      }
    },
    
    updateItem: async (id, data) => {
      try {
        const updatedItem = await itemService.updateItem(id, data);
        set(state => ({ 
          items: state.items.map(i => i.id === id ? updatedItem : i)
        }));
      } catch (error) {
        set(state => ({ 
          errors: { ...state.errors, items: 'خطأ في تحديث الصنف' }
        }));
      }
    },
    
    deleteItem: async (id) => {
      try {
        await itemService.deleteItem(id);
        set(state => ({ 
          items: state.items.filter(i => i.id !== id)
        }));
      } catch (error) {
        set(state => ({ 
          errors: { ...state.errors, items: 'خطأ في حذف الصنف' }
        }));
      }
    },
    
    // فواتير المبيعات
    loadSalesInvoices: async () => {
      set(state => ({ 
        loading: { ...state.loading, salesInvoices: true },
        errors: { ...state.errors, salesInvoices: undefined }
      }));
      
      try {
        const salesInvoices = await salesInvoiceService.getAllSalesInvoices();
        set(state => ({ 
          salesInvoices,
          loading: { ...state.loading, salesInvoices: false }
        }));
      } catch (error) {
        set(state => ({ 
          loading: { ...state.loading, salesInvoices: false },
          errors: { ...state.errors, salesInvoices: 'خطأ في تحميل فواتير المبيعات' }
        }));
      }
    },
    
    createSalesInvoice: async (data) => {
      try {
        const newInvoice = await salesInvoiceService.createSalesInvoice(data);
        set(state => ({ 
          salesInvoices: [newInvoice, ...state.salesInvoices]
        }));
      } catch (error) {
        set(state => ({ 
          errors: { ...state.errors, salesInvoices: 'خطأ في إنشاء فاتورة المبيعات' }
        }));
      }
    },
    
    // فواتير المشتريات
    loadPurchaseInvoices: async () => {
      set(state => ({ 
        loading: { ...state.loading, purchaseInvoices: true },
        errors: { ...state.errors, purchaseInvoices: undefined }
      }));
      
      try {
        const purchaseInvoices = await purchaseInvoiceService.getAllPurchaseInvoices();
        set(state => ({ 
          purchaseInvoices,
          loading: { ...state.loading, purchaseInvoices: false }
        }));
      } catch (error) {
        set(state => ({ 
          loading: { ...state.loading, purchaseInvoices: false },
          errors: { ...state.errors, purchaseInvoices: 'خطأ في تحميل فواتير المشتريات' }
        }));
      }
    },
    
    createPurchaseInvoice: async (data) => {
      try {
        const newInvoice = await purchaseInvoiceService.createPurchaseInvoice(data);
        set(state => ({ 
          purchaseInvoices: [newInvoice, ...state.purchaseInvoices]
        }));
      } catch (error) {
        set(state => ({ 
          errors: { ...state.errors, purchaseInvoices: 'خطأ في إنشاء فاتورة المشتريات' }
        }));
      }
    },
    
    // الإحصائيات
    loadStats: async () => {
      set(state => ({ 
        loading: { ...state.loading, stats: true },
        errors: { ...state.errors, stats: undefined }
      }));
      
      try {
        const [generalStats, salesStats] = await Promise.all([
          getGeneralStats(),
          getSalesStats()
        ]);
        
        set(state => ({ 
          stats: {
            ...generalStats,
            ...salesStats
          },
          loading: { ...state.loading, stats: false }
        }));
      } catch (error) {
        set(state => ({ 
          loading: { ...state.loading, stats: false },
          errors: { ...state.errors, stats: 'خطأ في تحميل الإحصائيات' }
        }));
      }
    },
    
    // تحميل جميع البيانات
    loadAllData: async () => {
      const { actions } = get();
      await Promise.all([
        actions.loadCustomers(),
        actions.loadSuppliers(),
        actions.loadItems(),
        actions.loadSalesInvoices(),
        actions.loadPurchaseInvoices(),
        actions.loadStats()
      ]);
    }
  }
})); 