import React from 'react'
import { NavLink } from 'react-router-dom'
import { 
  Home, 
  ShoppingCart, 
  Package, 
  Warehouse, 
  Calculator, 
  Users, 
  Factory, 
  CreditCard,
  Settings,
  Menu,
  X
} from 'lucide-react'

interface SidebarProps {
  isOpen: boolean
  onToggle: () => void
}

const Sidebar: React.FC<SidebarProps> = ({ isOpen, onToggle }) => {
  const menuItems = [
    {
      title: 'الرئيسية',
      path: '/dashboard',
      icon: Home,
      color: 'text-blue-600'
    },
    {
      title: 'المبيعات',
      path: '/sales',
      icon: ShoppingCart,
      color: 'text-green-600',
      children: [
        { title: 'فواتير المبيعات', path: '/sales/invoices' },
        { title: 'العملاء', path: '/sales/customers' }
      ]
    },
    {
      title: 'المشتريات',
      path: '/purchases',
      icon: Package,
      color: 'text-purple-600',
      children: [
        { title: 'فواتير المشتريات', path: '/purchases/invoices' },
        { title: 'الموردين', path: '/purchases/suppliers' }
      ]
    },
    {
      title: 'المخازن',
      path: '/inventory',
      icon: Warehouse,
      color: 'text-orange-600',
      children: [
        { title: 'نظرة عامة', path: '/inventory' },
        { title: 'الأصناف', path: '/inventory/items' },
        { title: 'حركة المخزون', path: '/inventory/movements' }
      ]
    },
    {
      title: 'الحسابات',
      path: '/accounting',
      icon: Calculator,
      color: 'text-red-600',
      children: [
        { title: 'دليل الحسابات', path: '/accounting/accounts' },
        { title: 'دفتر اليومية', path: '/accounting/journal' },
        { title: 'التقارير المالية', path: '/accounting/reports' }
      ]
    },
    {
      title: 'الرواتب',
      path: '/payroll',
      icon: Users,
      color: 'text-indigo-600',
      children: [
        { title: 'الموظفين', path: '/payroll/employees' },
        { title: 'سجلات الرواتب', path: '/payroll/records' }
      ]
    },
    {
      title: 'التصنيع',
      path: '/manufacturing',
      icon: Factory,
      color: 'text-teal-600',
      children: [
        { title: 'أوامر التصنيع', path: '/manufacturing/orders' }
      ]
    },
    {
      title: 'المصروفات',
      path: '/expenses',
      icon: CreditCard,
      color: 'text-pink-600'
    },
    {
      title: 'الإعدادات',
      path: '/settings',
      icon: Settings,
      color: 'text-gray-600',
      children: [
        { title: 'إعدادات الشركة', path: '/settings/company' },
        { title: 'إدارة المستخدمين', path: '/settings/users' }
      ]
    }
  ]

  return (
    <>
      {/* Overlay للشاشات الصغيرة */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onToggle}
        />
      )}

      {/* الشريط الجانبي */}
      <div className={`
        fixed right-0 top-0 h-full w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out z-50
        ${isOpen ? 'translate-x-0' : 'translate-x-full'}
      `}>
        {/* الهيدر */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center space-x-3 space-x-reverse">
            <div className="w-8 h-8 bg-gradient-libya rounded-lg flex items-center justify-center">
              <span className="text-white text-sm font-bold">أ</span>
            </div>
            <div>
              <h1 className="text-lg font-bold text-gray-800">Almamry ERP</h1>
              <p className="text-xs text-gray-500">نظام إدارة موارد المؤسسات</p>
            </div>
          </div>
          <button
            onClick={onToggle}
            className="p-1 rounded-lg hover:bg-gray-100 lg:hidden"
          >
            <X className="w-5 h-5 text-gray-600" />
          </button>
        </div>

        {/* قائمة التنقل */}
        <nav className="p-4 space-y-2">
          {menuItems.map((item) => (
            <div key={item.path}>
              <NavLink
                to={item.path}
                className={({ isActive }) => `
                  flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200
                  ${isActive 
                    ? 'bg-primary-100 text-primary-700' 
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }
                `}
              >
                <item.icon className={`w-5 h-5 ml-3 ${item.color}`} />
                {item.title}
              </NavLink>
              
              {/* القوائم الفرعية */}
              {item.children && (
                <div className="mr-8 mt-1 space-y-1">
                  {item.children.map((child) => (
                    <NavLink
                      key={child.path}
                      to={child.path}
                      className={({ isActive }) => `
                        block px-3 py-2 text-sm rounded-lg transition-colors duration-200
                        ${isActive 
                          ? 'bg-primary-50 text-primary-600' 
                          : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                        }
                      `}
                    >
                      {child.title}
                    </NavLink>
                  ))}
                </div>
              )}
            </div>
          ))}
        </nav>

        {/* معلومات النظام */}
        <div className="absolute bottom-0 right-0 left-0 p-4 border-t border-gray-200 bg-gray-50">
          <div className="text-center">
            <p className="text-xs text-gray-500">الإصدار 1.0.0</p>
            <p className="text-xs text-gray-400 mt-1">© 2024 Almamry ERP</p>
          </div>
        </div>
      </div>
    </>
  )
}

export default Sidebar 