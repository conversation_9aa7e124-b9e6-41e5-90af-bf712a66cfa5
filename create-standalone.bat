@echo off
chcp 65001 >nul
title إنشاء النسخة المستقلة - نظام المعماري ERP

echo.
echo ========================================
echo   إنشاء النسخة المستقلة
echo   نظام إدارة موارد المؤسسات
echo   شركة المعماري لصناعة الزجاج والألومنيوم
echo ========================================
echo.

set STANDALONE_DIR=AlmamryERP-Standalone
set DATE_TIME=%date:~-4,4%-%date:~3,2%-%date:~0,2%_%time:~0,2%-%time:~3,2%-%time:~6,2%
set DATE_TIME=%DATE_TIME: =0%

echo [1/6] إنشاء مجلد النسخة المستقلة...
if exist "%STANDALONE_DIR%" (
    echo حذف المجلد القديم...
    rmdir /s /q "%STANDALONE_DIR%"
)
mkdir "%STANDALONE_DIR%"
echo ✅ تم إنشاء المجلد

echo.
echo [2/6] نسخ الملفات الأساسية...
xcopy "src" "%STANDALONE_DIR%\src" /e /i /y
xcopy "electron" "%STANDALONE_DIR%\electron" /e /i /y
xcopy "prisma" "%STANDALONE_DIR%\prisma" /e /i /y
copy "package.json" "%STANDALONE_DIR%\"
copy "package-lock.json" "%STANDALONE_DIR%\"
copy "vite.config.ts" "%STANDALONE_DIR%\"
copy "tsconfig.json" "%STANDALONE_DIR%\"
copy "tsconfig.node.json" "%STANDALONE_DIR%\"
copy "tailwind.config.js" "%STANDALONE_DIR%\"
copy "postcss.config.js" "%STANDALONE_DIR%\"
copy "index.html" "%STANDALONE_DIR%\"
copy "index-standalone.html" "%STANDALONE_DIR%\"
echo ✅ تم نسخ الملفات الأساسية

echo.
echo [3/6] نسخ مجلد node_modules...
if exist "node_modules" (
    echo نسخ التبعيات (قد يستغرق وقتاً)...
    xcopy "node_modules" "%STANDALONE_DIR%\node_modules" /e /i /y
    echo ✅ تم نسخ التبعيات
) else (
    echo ⚠️ مجلد node_modules غير موجود، سيتم تثبيت التبعيات عند التشغيل
)

echo.
echo [4/6] نسخ ملفات التشغيل...
copy "run-portable.bat" "%STANDALONE_DIR%\"
copy "start-system.bat" "%STANDALONE_DIR%\"
copy "start-system.ps1" "%STANDALONE_DIR%\"
copy "QUICK-START.md" "%STANDALONE_DIR%\"
copy "README-PORTABLE.md" "%STANDALONE_DIR%\"
echo ✅ تم نسخ ملفات التشغيل

echo.
echo [5/6] إنشاء ملف تشغيل مستقل...
echo @echo off > "%STANDALONE_DIR%\run-standalone.bat"
echo chcp 65001 ^>nul >> "%STANDALONE_DIR%\run-standalone.bat"
echo title نظام المعماري ERP - النسخة المستقلة >> "%STANDALONE_DIR%\run-standalone.bat"
echo. >> "%STANDALONE_DIR%\run-standalone.bat"
echo echo. >> "%STANDALONE_DIR%\run-standalone.bat"
echo echo ======================================== >> "%STANDALONE_DIR%\run-standalone.bat"
echo echo   نظام إدارة موارد المؤسسات >> "%STANDALONE_DIR%\run-standalone.bat"
echo echo   شركة المعماري لصناعة الزجاج والألومنيوم >> "%STANDALONE_DIR%\run-standalone.bat"
echo echo   النسخة المستقلة >> "%STANDALONE_DIR%\run-standalone.bat"
echo echo ======================================== >> "%STANDALONE_DIR%\run-standalone.bat"
echo echo. >> "%STANDALONE_DIR%\run-standalone.bat"
echo. >> "%STANDALONE_DIR%\run-standalone.bat"
echo echo 🚀 بدء تشغيل النظام... >> "%STANDALONE_DIR%\run-standalone.bat"
echo echo. >> "%STANDALONE_DIR%\run-standalone.bat"
echo. >> "%STANDALONE_DIR%\run-standalone.bat"
echo echo [1/2] التحقق من قاعدة البيانات... >> "%STANDALONE_DIR%\run-standalone.bat"
echo if not exist "prisma\almamry_erp.db" ( >> "%STANDALONE_DIR%\run-standalone.bat"
echo     echo 🗄️ إنشاء قاعدة البيانات... >> "%STANDALONE_DIR%\run-standalone.bat"
echo     npx prisma db push --accept-data-loss --silent >> "%STANDALONE_DIR%\run-standalone.bat"
echo ) >> "%STANDALONE_DIR%\run-standalone.bat"
echo echo ✅ قاعدة البيانات جاهزة >> "%STANDALONE_DIR%\run-standalone.bat"
echo echo. >> "%STANDALONE_DIR%\run-standalone.bat"
echo. >> "%STANDALONE_DIR%\run-standalone.bat"
echo echo [2/2] تشغيل النظام... >> "%STANDALONE_DIR%\run-standalone.bat"
echo echo. >> "%STANDALONE_DIR%\run-standalone.bat"
echo echo 🌐 النظام سيفتح في المتصفح على: http://localhost:5173 >> "%STANDALONE_DIR%\run-standalone.bat"
echo echo 💻 تطبيق Electron سيفتح تلقائياً >> "%STANDALONE_DIR%\run-standalone.bat"
echo echo. >> "%STANDALONE_DIR%\run-standalone.bat"
echo echo 🔐 بيانات الدخول: admin / admin123 >> "%STANDALONE_DIR%\run-standalone.bat"
echo echo. >> "%STANDALONE_DIR%\run-standalone.bat"
echo echo ⚠️  لا تغلق هذا النافذة حتى تنتهي من استخدام النظام >> "%STANDALONE_DIR%\run-standalone.bat"
echo echo. >> "%STANDALONE_DIR%\run-standalone.bat"
echo. >> "%STANDALONE_DIR%\run-standalone.bat"
echo npm start >> "%STANDALONE_DIR%\run-standalone.bat"
echo echo. >> "%STANDALONE_DIR%\run-standalone.bat"
echo echo 👋 تم إغلاق النظام >> "%STANDALONE_DIR%\run-standalone.bat"
echo pause >> "%STANDALONE_DIR%\run-standalone.bat"
echo ✅ تم إنشاء ملف التشغيل المستقل

echo.
echo [6/6] إنشاء ملف ZIP للنسخة المستقلة...
powershell -command "Compress-Archive -Path '%STANDALONE_DIR%' -DestinationPath 'AlmamryERP-Standalone-%DATE_TIME%.zip' -Force"
if errorlevel 1 (
    echo ⚠️ فشل في إنشاء ملف ZIP، لكن النسخة المستقلة جاهزة في مجلد %STANDALONE_DIR%
) else (
    echo ✅ تم إنشاء ملف ZIP: AlmamryERP-Standalone-%DATE_TIME%.zip
)

echo.
echo ========================================
echo   ✅ تم إنشاء النسخة المستقلة بنجاح
echo ========================================
echo.
echo 📁 موقع النسخة المستقلة: %STANDALONE_DIR%
echo 📦 ملف ZIP: AlmamryERP-Standalone-%DATE_TIME%.zip
echo.
echo 🚀 لتشغيل النسخة المستقلة:
echo    1. انسخ مجلد %STANDALONE_DIR% إلى أي مكان
echo    2. انقر مرتين على run-standalone.bat
echo.
echo 📋 ملاحظات مهمة:
echo    - النسخة تحتوي على جميع التبعيات
echo    - تعمل بدون الحاجة لتثبيت Node.js منفصل
echo    - قاعدة البيانات محلية في prisma/almamry_erp.db
echo    - حجم الملف كبير بسبب تضمين التبعيات
echo.
pause 