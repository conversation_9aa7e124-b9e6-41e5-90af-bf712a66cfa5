@echo off
chcp 65001 >nul
title نظام إدارة موارد المؤسسات - شركة المعماري

echo.
echo ========================================
echo   نظام إدارة موارد المؤسسات
echo   شركة المعماري لصناعة الزجاج والألومنيوم
echo ========================================
echo.

echo [1/4] التحقق من وجود Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js غير مثبت. يرجى تثبيت Node.js أولاً.
    echo يمكنك تحميله من: https://nodejs.org/
    pause
    exit /b 1
)
echo ✅ Node.js مثبت

echo.
echo [2/4] التحقق من التبعيات...
if not exist "node_modules" (
    echo 📦 تثبيت التبعيات...
    npm install
    if errorlevel 1 (
        echo ❌ فشل في تثبيت التبعيات
        pause
        exit /b 1
    )
)
echo ✅ التبعيات جاهزة

echo.
echo [3/4] التحقق من قاعدة البيانات...
if not exist "prisma\almamry_erp.db" (
    echo 🗄️ إنشاء قاعدة البيانات...
    npx prisma db push
    if errorlevel 1 (
        echo ❌ فشل في إنشاء قاعدة البيانات
        pause
        exit /b 1
    )
)
echo ✅ قاعدة البيانات جاهزة

echo.
echo [4/4] تشغيل النظام...
echo.
echo 🌐 النظام سيفتح في المتصفح على: http://localhost:5173
echo 💻 تطبيق Electron سيفتح تلقائياً
echo.
echo ⚠️  لا تغلق هذا النافذة حتى تنتهي من استخدام النظام
echo.

npm start

echo.
echo 👋 تم إغلاق النظام
pause 