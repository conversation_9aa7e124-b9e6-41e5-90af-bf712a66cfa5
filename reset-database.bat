@echo off
echo ========================================
echo    إعادة تعيين قاعدة البيانات
echo ========================================
echo.

echo 🔄 حذف قاعدة البيانات الحالية...
if exist "prisma\almamry_erp.db" (
    del "prisma\almamry_erp.db"
    echo ✅ تم حذف قاعدة البيانات القديمة
) else (
    echo ⚠️ قاعدة البيانات غير موجودة
)

echo.
echo 🔄 تطبيق migrations...
call npx prisma migrate deploy
if %errorlevel% neq 0 (
    echo ❌ فشل في تطبيق migrations
    echo 🔄 محاولة إنشاء قاعدة البيانات يدوياً...
    call node scripts\init-database.js
) else (
    echo ✅ تم تطبيق migrations بنجاح
)

echo.
echo 🔄 تشغيل seed script...
call npx prisma db seed
if %errorlevel% neq 0 (
    echo ⚠️ تحذير: فشل في تشغيل seed script
    echo 🔄 تشغيل سكريبت التهيئة البديل...
    call node scripts\init-database.js
)

echo.
echo 🔄 إنشاء Prisma Client...
call npx prisma generate

echo.
echo ✅ تم إعادة تعيين قاعدة البيانات بنجاح!
echo.
pause
