import prisma from '../lib/database';
import type { User, Customer, Supplier, Item, SalesInvoice, PurchaseInvoice } from '@prisma/client';

// ===== خدمات المستخدمين =====
export const userService = {
  // تسجيل الدخول
  async login(username: string, password: string) {
    const user = await prisma.user.findUnique({
      where: { username }
    });
    
    if (user && user.password === password && user.isActive) {
      const { password: _, ...userWithoutPassword } = user;
      return userWithoutPassword;
    }
    return null;
  },

  // الحصول على جميع المستخدمين
  async getAllUsers() {
    return await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        fullName: true,
        email: true,
        role: true,
        isActive: true,
        createdAt: true,
      }
    });
  },

  // إنشاء مستخدم جديد
  async createUser(userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>) {
    return await prisma.user.create({
      data: userData,
      select: {
        id: true,
        username: true,
        fullName: true,
        email: true,
        role: true,
        isActive: true,
        createdAt: true,
      }
    });
  }
};

// ===== خدمات العملاء =====
export const customerService = {
  // الحصول على جميع العملاء
  async getAllCustomers() {
    return await prisma.customer.findMany({
      orderBy: { name: 'asc' }
    });
  },

  // إنشاء عميل جديد
  async createCustomer(customerData: Omit<Customer, 'id' | 'createdAt' | 'updatedAt' | 'balance'>) {
    return await prisma.customer.create({
      data: customerData
    });
  },

  // تحديث عميل
  async updateCustomer(id: string, customerData: Partial<Customer>) {
    return await prisma.customer.update({
      where: { id },
      data: customerData
    });
  },

  // حذف عميل
  async deleteCustomer(id: string) {
    return await prisma.customer.delete({
      where: { id }
    });
  }
};

// ===== خدمات الموردين =====
export const supplierService = {
  // الحصول على جميع الموردين
  async getAllSuppliers() {
    return await prisma.supplier.findMany({
      orderBy: { name: 'asc' }
    });
  },

  // إنشاء مورد جديد
  async createSupplier(supplierData: Omit<Supplier, 'id' | 'createdAt' | 'updatedAt' | 'balance'>) {
    return await prisma.supplier.create({
      data: supplierData
    });
  },

  // تحديث مورد
  async updateSupplier(id: string, supplierData: Partial<Supplier>) {
    return await prisma.supplier.update({
      where: { id },
      data: supplierData
    });
  },

  // حذف مورد
  async deleteSupplier(id: string) {
    return await prisma.supplier.delete({
      where: { id }
    });
  }
};

// ===== خدمات الأصناف =====
export const itemService = {
  // الحصول على جميع الأصناف
  async getAllItems() {
    return await prisma.item.findMany({
      orderBy: { name: 'asc' }
    });
  },

  // إنشاء صنف جديد
  async createItem(itemData: Omit<Item, 'id' | 'createdAt' | 'updatedAt'>) {
    return await prisma.item.create({
      data: itemData
    });
  },

  // تحديث صنف
  async updateItem(id: string, itemData: Partial<Item>) {
    return await prisma.item.update({
      where: { id },
      data: itemData
    });
  },

  // حذف صنف
  async deleteItem(id: string) {
    return await prisma.item.delete({
      where: { id }
    });
  }
};

// ===== خدمات فواتير المبيعات =====
export const salesInvoiceService = {
  // الحصول على جميع فواتير المبيعات
  async getAllSalesInvoices() {
    return await prisma.salesInvoice.findMany({
      include: {
        customer: true,
        user: {
          select: {
            id: true,
            fullName: true
          }
        },
        items: {
          include: {
            item: true
          }
        }
      },
      orderBy: { invoiceDate: 'desc' }
    });
  },

  // إنشاء فاتورة مبيعات جديدة
  async createSalesInvoice(invoiceData: any) {
    return await prisma.salesInvoice.create({
      data: {
        ...invoiceData,
        items: {
          create: invoiceData.items
        }
      },
      include: {
        customer: true,
        items: {
          include: {
            item: true
          }
        }
      }
    });
  }
};

// ===== خدمات فواتير المشتريات =====
export const purchaseInvoiceService = {
  // الحصول على جميع فواتير المشتريات
  async getAllPurchaseInvoices() {
    return await prisma.purchaseInvoice.findMany({
      include: {
        supplier: true,
        user: {
          select: {
            id: true,
            fullName: true
          }
        },
        items: {
          include: {
            item: true
          }
        }
      },
      orderBy: { invoiceDate: 'desc' }
    });
  },

  // إنشاء فاتورة مشتريات جديدة
  async createPurchaseInvoice(invoiceData: any) {
    return await prisma.purchaseInvoice.create({
      data: {
        ...invoiceData,
        items: {
          create: invoiceData.items
        }
      },
      include: {
        supplier: true,
        items: {
          include: {
            item: true
          }
        }
      }
    });
  }
};

// ===== خدمات الإحصائيات =====
export const statsService = {
  // إحصائيات عامة
  async getGeneralStats() {
    const [
      customersCount,
      suppliersCount,
      itemsCount,
      salesInvoicesCount,
      purchaseInvoicesCount
    ] = await Promise.all([
      prisma.customer.count(),
      prisma.supplier.count(),
      prisma.item.count(),
      prisma.salesInvoice.count(),
      prisma.purchaseInvoice.count()
    ]);

    return {
      customersCount,
      suppliersCount,
      itemsCount,
      salesInvoicesCount,
      purchaseInvoicesCount
    };
  },

  // إحصائيات المبيعات
  async getSalesStats() {
    const totalSales = await prisma.salesInvoice.aggregate({
      _sum: { total: true }
    });

    const monthlySales = await prisma.salesInvoice.groupBy({
      by: ['invoiceDate'],
      _sum: { total: true },
      where: {
        invoiceDate: {
          gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
        }
      }
    });

    return {
      totalSales: totalSales._sum.total || 0,
      monthlySales
    };
  }
}; 