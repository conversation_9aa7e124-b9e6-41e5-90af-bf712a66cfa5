# نظام إدارة موارد المؤسسات - شركة المعماري
# تشغيل النظام المحمول

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  نظام إدارة موارد المؤسسات" -ForegroundColor White
Write-Host "  شركة المعماري لصناعة الزجاج والألومنيوم" -ForegroundColor White
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# التحقق من وجود Node.js
Write-Host "[1/4] التحقق من وجود Node.js..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js مثبت - الإصدار: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js غير مثبت. يرجى تثبيت Node.js أولاً." -ForegroundColor Red
    Write-Host "يمكنك تحميله من: https://nodejs.org/" -ForegroundColor Yellow
    Read-Host "اضغط Enter للخروج"
    exit 1
}

Write-Host ""

# التحقق من التبعيات
Write-Host "[2/4] التحقق من التبعيات..." -ForegroundColor Yellow
if (-not (Test-Path "node_modules")) {
    Write-Host "📦 تثبيت التبعيات..." -ForegroundColor Blue
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ فشل في تثبيت التبعيات" -ForegroundColor Red
        Read-Host "اضغط Enter للخروج"
        exit 1
    }
}
Write-Host "✅ التبعيات جاهزة" -ForegroundColor Green

Write-Host ""

# التحقق من قاعدة البيانات
Write-Host "[3/4] التحقق من قاعدة البيانات..." -ForegroundColor Yellow
if (-not (Test-Path "prisma\almamry_erp.db")) {
    Write-Host "🗄️ إنشاء قاعدة البيانات..." -ForegroundColor Blue
    npx prisma db push
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ فشل في إنشاء قاعدة البيانات" -ForegroundColor Red
        Read-Host "اضغط Enter للخروج"
        exit 1
    }
}
Write-Host "✅ قاعدة البيانات جاهزة" -ForegroundColor Green

Write-Host ""

# تشغيل النظام
Write-Host "[4/4] تشغيل النظام..." -ForegroundColor Yellow
Write-Host ""
Write-Host "🌐 النظام سيفتح في المتصفح على: http://localhost:5173" -ForegroundColor Cyan
Write-Host "💻 تطبيق Electron سيفتح تلقائياً" -ForegroundColor Cyan
Write-Host ""
Write-Host "⚠️  لا تغلق هذا النافذة حتى تنتهي من استخدام النظام" -ForegroundColor Yellow
Write-Host ""

npm start

Write-Host ""
Write-Host "👋 تم إغلاق النظام" -ForegroundColor Green
Read-Host "اضغط Enter للخروج" 