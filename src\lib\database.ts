import { PrismaClient } from '@prisma/client';

// إنشاء عميل Prisma واحد للتطبيق بأكمله
const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

export const prisma = globalForPrisma.prisma ?? new PrismaClient();

if (process.env.NODE_ENV !== 'production') {
  globalForPrisma.prisma = prisma;
}

// دالة لتهيئة قاعدة البيانات
export async function initializeDatabase() {
  try {
    // التحقق من الاتصال
    await prisma.$connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');
    
    // التحقق من وجود إعدادات النظام
    const settings = await prisma.systemSettings.findFirst();
    if (!settings) {
      // إنشاء إعدادات افتراضية
      await prisma.systemSettings.create({
        data: {
          companyName: 'شركة المامري للزجاج والألمنيوم',
          companyAddress: 'طرابلس، ليبيا',
          companyPhone: '+218-21-1234567',
          companyEmail: '<EMAIL>',
          currency: 'دينار ليبي',
          currencySymbol: 'ل.د',
        }
      });
      console.log('✅ تم إنشاء إعدادات النظام الافتراضية');
    }

    // التحقق من وجود مستخدم مدير
    const adminUser = await prisma.user.findFirst({
      where: { role: 'ADMIN' }
    });
    
    if (!adminUser) {
      // إنشاء مستخدم مدير افتراضي
      await prisma.user.create({
        data: {
          username: 'admin',
          password: 'admin123', // في الإنتاج يجب تشفير كلمة المرور
          fullName: 'مدير النظام',
          email: '<EMAIL>',
          role: 'ADMIN',
        }
      });
      console.log('✅ تم إنشاء مستخدم المدير الافتراضي');
    }

    return true;
  } catch (error) {
    console.error('❌ خطأ في تهيئة قاعدة البيانات:', error);
    return false;
  }
}

// دالة لإغلاق الاتصال
export async function disconnectDatabase() {
  await prisma.$disconnect();
}

// تصدير العميل للاستخدام في أجزاء أخرى من التطبيق
export default prisma; 