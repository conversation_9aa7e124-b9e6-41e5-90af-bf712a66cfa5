import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { User } from '@/types'

interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
}

interface AuthActions {
  login: (username: string, password: string) => Promise<boolean>
  logout: () => void
  checkAuth: () => Promise<void>
  setUser: (user: User) => void
  setToken: (token: string) => void
  setLoading: (loading: boolean) => void
}

type AuthStore = AuthState & AuthActions

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // الحالة الأولية
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,

      // تسجيل الدخول
      login: async (username: string, password: string) => {
        set({ isLoading: true })
        
        try {
          // محاكاة API call
          await new Promise(resolve => setTimeout(resolve, 1000))
          
          // بيانات المستخدم الافتراضية (في التطبيق الحقيقي ستأتي من API)
          const mockUser: User = {
            id: '1',
            username: 'admin',
            fullName: 'مدير النظام',
            email: '<EMAIL>',
            role: 'ADMIN' as any,
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date()
          }
          
          const mockToken = 'mock-jwt-token'
          
          set({
            user: mockUser,
            token: mockToken,
            isAuthenticated: true,
            isLoading: false
          })
          
          return true
        } catch (error) {
          set({ isLoading: false })
          return false
        }
      },

      // تسجيل الخروج
      logout: () => {
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false
        })
      },

      // فحص حالة المصادقة
      checkAuth: async () => {
        const { token, user } = get()
        
        if (token && user) {
          set({ isAuthenticated: true })
        } else {
          set({ isAuthenticated: false })
        }
      },

      // تعيين المستخدم
      setUser: (user: User) => {
        set({ user })
      },

      // تعيين التوكن
      setToken: (token: string) => {
        set({ token })
      },

      // تعيين حالة التحميل
      setLoading: (loading: boolean) => {
        set({ isLoading: loading })
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated
      })
    }
  )
) 