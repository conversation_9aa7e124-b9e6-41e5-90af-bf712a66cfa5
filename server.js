const express = require('express');
const cors = require('cors');
const { PrismaClient } = require('@prisma/client');

const app = express();
const prisma = new PrismaClient();
const PORT = 4000;

app.use(cors());
app.use(express.json());

// Endpoint: جلب كل العملاء
app.get('/api/customers', async (req, res) => {
  try {
    const customers = await prisma.customer.findMany({ orderBy: { name: 'asc' } });
    res.json(customers);
  } catch (error) {
    res.status(500).json({ error: 'خطأ في جلب العملاء', details: error.message });
  }
});

// Endpoint: جلب كل الموردين (مثال إضافي)
app.get('/api/suppliers', async (req, res) => {
  try {
    const suppliers = await prisma.supplier.findMany({ orderBy: { name: 'asc' } });
    res.json(suppliers);
  } catch (error) {
    res.status(500).json({ error: 'خطأ في جلب الموردين', details: error.message });
  }
});

// Endpoint: جلب كل الأصناف (مثال إضافي)
app.get('/api/items', async (req, res) => {
  try {
    const items = await prisma.item.findMany({ orderBy: { name: 'asc' } });
    res.json(items);
  } catch (error) {
    res.status(500).json({ error: 'خطأ في جلب الأصناف', details: error.message });
  }
});

app.listen(PORT, () => {
  console.log(`✅ Backend API يعمل على http://localhost:${PORT}`);
}); 