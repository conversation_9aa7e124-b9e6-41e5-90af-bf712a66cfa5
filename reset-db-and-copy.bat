@echo off
REM سكربت تلقائي لإعادة بناء قاعدة البيانات ونسخها للنسخة المحمولة

REM 1. الانتقال لمجلد المشروع
cd /d "%~dp0"

REM 2. حذف قاعدة البيانات القديمة (إن وجدت)
if exist "prisma\almamry_erp.db" del /f /q "prisma\almamry_erp.db"

REM 3. إعادة بناء الجداول
npx --no-install prisma db push
if errorlevel 1 (
    echo حدث خطأ أثناء إنشاء الجداول. تأكد أن جميع الحزم مثبتة.
    pause
    exit /b 1
)

REM 4. تعبئة بيانات افتراضية (admin) عبر سكربت seed
npx --no-install ts-node prisma/seed.ts
if errorlevel 1 (
    echo حدث خطأ أثناء تعبئة البيانات الافتراضية.
    pause
    exit /b 1
)

REM 5. التأكد من وجود مجلد النسخة المحمولة
if not exist "dist\win-unpacked\prisma" mkdir "dist\win-unpacked\prisma"

REM 6. نسخ قاعدة البيانات للنسخة المحمولة
copy /y "prisma\almamry_erp.db" "dist\win-unpacked\prisma\almamry_erp.db"

echo ----------------------------------------
echo تم إنشاء قاعدة البيانات ونسخها بنجاح!
echo يمكنك الآن تشغيل النسخة المحمولة بدون مشاكل.
pause 