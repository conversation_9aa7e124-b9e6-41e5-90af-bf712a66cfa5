<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alma<PERSON>ry ERP - نظام إدارة موارد المؤسسات</title>
    
    <!-- Tailwind CSS من CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- خطوط Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- أيقونات Lucide -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    
    <!-- Chart.js للرسوم البيانية -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            direction: rtl;
            background: #f8fafc;
        }
        
        .sidebar {
            position: fixed;
            right: 0;
            top: 0;
            height: 100vh;
            width: 280px;
            background: white;
            box-shadow: -2px 0 10px rgba(0,0,0,0.1);
            z-index: 50;
            transition: transform 0.3s ease;
            display: flex;
            flex-direction: column;
        }
        
        .sidebar-header {
            flex-shrink: 0;
        }
        
        .sidebar-nav {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
        }
        
        .sidebar-nav::-webkit-scrollbar {
            width: 6px;
        }
        
        .sidebar-nav::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }
        
        .sidebar-nav::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        
        .sidebar-nav::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
        
        .main-content {
            margin-right: 280px;
            transition: margin-right 0.3s ease;
        }
        
        .main-content.full {
            margin-right: 0;
        }
        
        @media (max-width: 1023px) {
            .sidebar {
                top: 48px;
                height: calc(100vh - 48px);
            }
            
            .main-content {
                margin-top: 48px;
            }
        }
        
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border: 1px solid #e5e7eb;
        }
        
        .card-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .card-body {
            padding: 1.5rem;
        }
        
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-weight: 500;
            transition: all 0.2s;
            cursor: pointer;
            border: none;
            text-decoration: none;
        }
        
        .btn-primary {
            background: #0ea5e9;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0284c7;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            color: #6b7280;
            text-decoration: none;
            border-radius: 0.5rem;
            margin: 0.25rem 0.5rem;
            transition: all 0.2s;
        }
        
        .nav-link:hover {
            background: #f3f4f6;
            color: #374151;
        }
        
        .nav-link.active {
            background: #dbeafe;
            color: #1d4ed8;
        }
        
        .nav-section {
            margin-bottom: 0.5rem;
        }
        
        .nav-section-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0.5rem 1rem;
            color: #6b7280;
            font-size: 0.75rem;
            font-weight: 600;
            cursor: pointer;
            border-radius: 0.5rem;
            margin: 0.25rem 0.5rem;
            transition: all 0.2s;
        }
        
        .nav-section-header:hover {
            background: #f3f4f6;
            color: #374151;
        }
        
        .nav-section-header .section-icon {
            transition: transform 0.2s;
            transform: rotate(-90deg);
        }
        
        .nav-section-header.expanded .section-icon {
            transform: rotate(0deg);
        }
        
        .nav-section-content {
            overflow: hidden;
            transition: max-height 0.3s ease;
            max-height: 500px;
        }
        
        .nav-section-content.collapsed {
            max-height: 0;
        }
        
        .nav-section-content .nav-link {
            margin-right: 1rem;
            margin-left: 0.5rem;
            font-size: 0.875rem;
        }
        
        .page {
            display: none;
        }
        
        .page.active {
            display: block;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border: 1px solid #e5e7eb;
        }
        
        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #6b7280;
            font-size: 0.875rem;
        }
        
        .kpi-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            padding: 1.5rem;
            position: relative;
            overflow: hidden;
        }
        
        .kpi-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            transform: translate(30px, -30px);
        }
        
        .kpi-card.sales {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .kpi-card.purchases {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        
        .kpi-card.inventory {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
        
        .kpi-card.profit {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #374151;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin: 1rem 0;
        }
        
        .trend-indicator {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .trend-up {
            background: #dcfce7;
            color: #166534;
        }
        
        .trend-down {
            background: #fee2e2;
            color: #dc2626;
        }
        
        .trend-neutral {
            background: #f3f4f6;
            color: #6b7280;
        }
        
        .module-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .module-stat {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border: 1px solid #e5e7eb;
        }
        
        .module-stat-header {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        
        .module-stat-icon {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 0.5rem;
        }
        
        .module-stat-title {
            font-size: 0.875rem;
            font-weight: 500;
            color: #6b7280;
        }
        
        .module-stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #1f2937;
        }
        
        .company-banner {
            background: linear-gradient(90deg, #1e40af, #3b82f6, #1e40af);
            background-size: 200% 100%;
            animation: gradientMove 3s ease-in-out infinite;
            color: white;
            padding: 0.25rem 0.75rem;
            text-align: center;
            font-weight: 600;
            font-size: 0.75rem;
            position: relative;
            overflow: hidden;
            border-radius: 0.375rem;
            display: inline-block;
        }
        
        .company-banner::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            animation: shimmer 2s infinite;
        }
        
        @keyframes gradientMove {
            0%, 100% {
                background-position: 0% 50%;
            }
            50% {
                background-position: 100% 50%;
            }
        }
        
        @keyframes shimmer {
            0% {
                left: -100%;
            }
            100% {
                left: 100%;
            }
        }
        
        .company-name {
            display: inline-block;
            animation: textGlow 2s ease-in-out infinite alternate;
        }
        
        .glass-decoration {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 120px;
            background: linear-gradient(135deg, 
                rgba(59, 130, 246, 0.1) 0%,
                rgba(147, 51, 234, 0.05) 50%,
                rgba(59, 130, 246, 0.1) 100%);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(59, 130, 246, 0.3);
            overflow: hidden;
            pointer-events: none;
        }
        
        .glass-panel {
            position: absolute;
            width: 60px;
            height: 80px;
            background: linear-gradient(135deg,
                rgba(59, 130, 246, 0.4) 0%,
                rgba(147, 51, 234, 0.2) 50%,
                rgba(59, 130, 246, 0.1) 100%);
            border: 1px solid rgba(59, 130, 246, 0.4);
            border-radius: 8px;
            backdrop-filter: blur(5px);
            box-shadow: 
                0 4px 6px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }
        
        .glass-panel-1 {
            bottom: 20px;
            left: 20px;
            transform: rotate(-5deg);
            animation: glassFloat 4s ease-in-out infinite;
            background: linear-gradient(135deg,
                rgba(59, 130, 246, 0.5) 0%,
                rgba(147, 51, 234, 0.3) 50%,
                rgba(59, 130, 246, 0.2) 100%);
            border: 1px solid rgba(59, 130, 246, 0.5);
        }
        
        .glass-panel-2 {
            bottom: 30px;
            left: 90px;
            transform: rotate(3deg);
            animation: glassFloat 4s ease-in-out infinite 1s;
            background: linear-gradient(135deg,
                rgba(147, 51, 234, 0.5) 0%,
                rgba(59, 130, 246, 0.3) 50%,
                rgba(147, 51, 234, 0.2) 100%);
            border: 1px solid rgba(147, 51, 234, 0.5);
        }
        
        .glass-panel-3 {
            bottom: 25px;
            right: 20px;
            transform: rotate(-2deg);
            animation: glassFloat 4s ease-in-out infinite 2s;
            background: linear-gradient(135deg,
                rgba(59, 130, 246, 0.4) 0%,
                rgba(147, 51, 234, 0.2) 50%,
                rgba(59, 130, 246, 0.1) 100%);
            border: 1px solid rgba(59, 130, 246, 0.4);
        }
        
        .glass-reflection {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 100%;
            background: linear-gradient(90deg,
                transparent 0%,
                rgba(255, 255, 255, 0.3) 50%,
                transparent 100%);
            animation: reflectionMove 3s ease-in-out infinite;
        }
        
        @keyframes glassFloat {
            0%, 100% {
                transform: translateY(0px) rotate(var(--rotation));
            }
            50% {
                transform: translateY(-5px) rotate(var(--rotation));
            }
        }
        
        @keyframes reflectionMove {
            0% {
                transform: translateX(-100%);
            }
            100% {
                transform: translateX(100%);
            }
        }
        
        .glass-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 100%;
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.6) 0%,
                transparent 50%,
                rgba(255, 255, 255, 0.2) 100%);
            border-radius: 8px;
        }
        
        .glass-panel::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            right: 2px;
            height: 2px;
            background: linear-gradient(90deg,
                transparent 0%,
                rgba(255, 255, 255, 0.8) 50%,
                transparent 100%);
            border-radius: 1px;
        }
        
        @keyframes textGlow {
            from {
                text-shadow: 0 0 5px rgba(255,255,255,0.5);
            }
            to {
                text-shadow: 0 0 20px rgba(255,255,255,0.8), 0 0 30px rgba(255,255,255,0.6);
            }
        }
        
        .clock-container {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .analog-clock {
            width: 40px;
            height: 40px;
            border: 2px solid #3b82f6;
            border-radius: 50%;
            position: relative;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .clock-center {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 4px;
            height: 4px;
            background: #3b82f6;
            border-radius: 50%;
            transform: translate(-50%, -50%);
        }
        
        .hour-hand {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 2px;
            height: 8px;
            background: #1e40af;
            border-radius: 1px;
            transform-origin: bottom center;
            transform: translate(-50%, -100%);
        }
        
        .minute-hand {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 1.5px;
            height: 12px;
            background: #3b82f6;
            border-radius: 0.75px;
            transform-origin: bottom center;
            transform: translate(-50%, -100%);
        }
        
        .second-hand {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 1px;
            height: 14px;
            background: #ef4444;
            border-radius: 0.5px;
            transform-origin: bottom center;
            transform: translate(-50%, -100%);
        }
        
        .date-time-info {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }
        
        .current-time {
            font-size: 0.875rem;
            font-weight: 600;
            color: #1f2937;
        }
        
        .current-date {
            font-size: 0.75rem;
            color: #6b7280;
        }
        
        .sidebar.closed {
            transform: translateX(100%);
        }
        
        @media (max-width: 1023px) {
            .sidebar {
                top: 48px;
                height: calc(100vh - 48px);
            }
            
            .main-content {
                margin-top: 48px;
            }
        }
    </style>
</head>
<body>
    <!-- الشريط الجانبي -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="flex items-center space-x-3 space-x-reverse">
                <div class="w-8 h-8 bg-gradient-to-r from-red-600 to-green-600 rounded-lg flex items-center justify-center">
                    <span class="text-white text-sm font-bold">أ</span>
                </div>
                <div>
                    <h1 class="text-lg font-bold text-gray-800">Almamry ERP</h1>
                    <p class="text-xs text-gray-500">نظام إدارة موارد المؤسسات</p>
                </div>
            </div>
        </div>
        
        <nav class="sidebar-nav">
            <a href="#" class="nav-link active" data-page="dashboard">
                <i data-lucide="home" class="w-5 h-5 ml-3"></i>
                الرئيسية
            </a>
            
            <div class="nav-section">
                <div class="nav-section-header" data-section="sales">
                    <span>المبيعات</span>
                    <i data-lucide="chevron-down" class="w-4 h-4 section-icon"></i>
                </div>
                <div class="nav-section-content collapsed" id="sales-content">
                    <a href="#" class="nav-link" data-page="sales-invoices">
                        <i data-lucide="shopping-cart" class="w-4 h-4 ml-2"></i>
                        فواتير المبيعات
                    </a>
                    <a href="#" class="nav-link" data-page="customers">
                        <i data-lucide="users" class="w-4 h-4 ml-2"></i>
                        العملاء
                    </a>
                    <a href="#" class="nav-link" data-page="sales-reports">
                        <i data-lucide="bar-chart" class="w-4 h-4 ml-2"></i>
                        تقارير المبيعات
                    </a>
                </div>
            </div>
            
            <div class="nav-section">
                <div class="nav-section-header" data-section="purchases">
                    <span>المشتريات</span>
                    <i data-lucide="chevron-down" class="w-4 h-4 section-icon"></i>
                </div>
                <div class="nav-section-content collapsed" id="purchases-content">
                    <a href="#" class="nav-link" data-page="purchase-invoices">
                        <i data-lucide="package" class="w-4 h-4 ml-2"></i>
                        فواتير المشتريات
                    </a>
                    <a href="#" class="nav-link" data-page="suppliers">
                        <i data-lucide="truck" class="w-4 h-4 ml-2"></i>
                        الموردين
                    </a>
                    <a href="#" class="nav-link" data-page="purchase-reports">
                        <i data-lucide="trending-up" class="w-4 h-4 ml-2"></i>
                        تقارير المشتريات
                    </a>
                </div>
            </div>
            
            <div class="nav-section">
                <div class="nav-section-header" data-section="inventory">
                    <span>المخازن</span>
                    <i data-lucide="chevron-down" class="w-4 h-4 section-icon"></i>
                </div>
                <div class="nav-section-content collapsed" id="inventory-content">
                    <a href="#" class="nav-link" data-page="inventory">
                        <i data-lucide="warehouse" class="w-4 h-4 ml-2"></i>
                        المخازن
                    </a>
                    <a href="#" class="nav-link" data-page="items">
                        <i data-lucide="package" class="w-4 h-4 ml-2"></i>
                        الأصناف
                    </a>
                    <a href="#" class="nav-link" data-page="inventory-movements">
                        <i data-lucide="move" class="w-4 h-4 ml-2"></i>
                        حركة المخزون
                    </a>
                    <a href="#" class="nav-link" data-page="inventory-reports">
                        <i data-lucide="clipboard-list" class="w-4 h-4 ml-2"></i>
                        تقارير المخزون
                    </a>
                </div>
            </div>
            
            <div class="nav-section">
                <div class="nav-section-header" data-section="accounting">
                    <span>الحسابات</span>
                    <i data-lucide="chevron-down" class="w-4 h-4 section-icon"></i>
                </div>
                <div class="nav-section-content collapsed" id="accounting-content">
                    <a href="#" class="nav-link" data-page="accounts">
                        <i data-lucide="calculator" class="w-4 h-4 ml-2"></i>
                        دليل الحسابات
                    </a>
                    <a href="#" class="nav-link" data-page="journal">
                        <i data-lucide="book-open" class="w-4 h-4 ml-2"></i>
                        دفتر اليومية
                    </a>
                    <a href="#" class="nav-link" data-page="financial-reports">
                        <i data-lucide="pie-chart" class="w-4 h-4 ml-2"></i>
                        التقارير المالية
                    </a>
                    <a href="#" class="nav-link" data-page="balance-sheet">
                        <i data-lucide="file-text" class="w-4 h-4 ml-2"></i>
                        الميزانية العمومية
                    </a>
                    <a href="#" class="nav-link" data-page="income-statement">
                        <i data-lucide="trending-down" class="w-4 h-4 ml-2"></i>
                        قائمة الدخل
                    </a>
                </div>
            </div>
            
            <div class="nav-section">
                <div class="nav-section-header" data-section="payroll">
                    <span>الرواتب</span>
                    <i data-lucide="chevron-down" class="w-4 h-4 section-icon"></i>
                </div>
                <div class="nav-section-content collapsed" id="payroll-content">
                    <a href="#" class="nav-link" data-page="employees">
                        <i data-lucide="user-check" class="w-4 h-4 ml-2"></i>
                        الموظفين
                    </a>
                    <a href="#" class="nav-link" data-page="payroll">
                        <i data-lucide="credit-card" class="w-4 h-4 ml-2"></i>
                        الرواتب
                    </a>
                    <a href="#" class="nav-link" data-page="attendance">
                        <i data-lucide="clock" class="w-4 h-4 ml-2"></i>
                        الحضور والانصراف
                    </a>
                    <a href="#" class="nav-link" data-page="payroll-reports">
                        <i data-lucide="file-bar-chart" class="w-4 h-4 ml-2"></i>
                        تقارير الرواتب
                    </a>
                </div>
            </div>
            
            <div class="nav-section">
                <div class="nav-section-header" data-section="manufacturing">
                    <span>التصنيع</span>
                    <i data-lucide="chevron-down" class="w-4 h-4 section-icon"></i>
                </div>
                <div class="nav-section-content collapsed" id="manufacturing-content">
                    <a href="#" class="nav-link" data-page="manufacturing">
                        <i data-lucide="factory" class="w-4 h-4 ml-2"></i>
                        أوامر التصنيع
                    </a>
                    <a href="#" class="nav-link" data-page="production-planning">
                        <i data-lucide="calendar" class="w-4 h-4 ml-2"></i>
                        تخطيط الإنتاج
                    </a>
                    <a href="#" class="nav-link" data-page="quality-control">
                        <i data-lucide="check-circle" class="w-4 h-4 ml-2"></i>
                        مراقبة الجودة
                    </a>
                </div>
            </div>
            
            <div class="nav-section">
                <div class="nav-section-header" data-section="expenses">
                    <span>المصروفات</span>
                    <i data-lucide="chevron-down" class="w-4 h-4 section-icon"></i>
                </div>
                <div class="nav-section-content collapsed" id="expenses-content">
                    <a href="#" class="nav-link" data-page="expenses">
                        <i data-lucide="receipt" class="w-4 h-4 ml-2"></i>
                        المصروفات
                    </a>
                    <a href="#" class="nav-link" data-page="expense-categories">
                        <i data-lucide="folder" class="w-4 h-4 ml-2"></i>
                        فئات المصروفات
                    </a>
                    <a href="#" class="nav-link" data-page="expense-reports">
                        <i data-lucide="file-spreadsheet" class="w-4 h-4 ml-2"></i>
                        تقارير المصروفات
                    </a>
                </div>
            </div>
            
            <div class="nav-section">
                <div class="nav-section-header" data-section="settings">
                    <span>الإعدادات</span>
                    <i data-lucide="chevron-down" class="w-4 h-4 section-icon"></i>
                </div>
                <div class="nav-section-content collapsed" id="settings-content">
                    <a href="#" class="nav-link" data-page="settings">
                        <i data-lucide="settings" class="w-4 h-4 ml-2"></i>
                        الإعدادات العامة
                    </a>
                    <a href="#" class="nav-link" data-page="users">
                        <i data-lucide="user-cog" class="w-4 h-4 ml-2"></i>
                        إدارة المستخدمين
                    </a>
                    <a href="#" class="nav-link" data-page="backup">
                        <i data-lucide="database" class="w-4 h-4 ml-2"></i>
                        النسخ الاحتياطي
                    </a>
                    <a href="#" class="nav-link" data-page="system-info">
                        <i data-lucide="info" class="w-4 h-4 ml-2"></i>
                        معلومات النظام
                    </a>
                </div>
            </div>
        </nav>
        <div style="padding: 1.5rem 1rem 1.5rem 1rem; text-align: center;">
            <div style="background: linear-gradient(90deg, #1e40af, #3b82f6, #1e40af); color: white; border-radius: 0.75rem; font-weight: bold; font-size: 1rem; padding: 0.5rem 0; box-shadow: 0 2px 8px rgba(59,130,246,0.08); letter-spacing: 1px;">
                شركة المعماري لصناعة الزجاج والألومنيوم
            </div>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content" id="mainContent">
        <!-- الهيدر -->
        <header class="bg-white shadow-sm border-b border-gray-200">
            <div class="flex items-center justify-between px-6 py-4">
                <div class="flex items-center space-x-4 space-x-reverse">
                    <button onclick="toggleSidebar()" class="p-2 rounded-lg hover:bg-gray-100">
                        <i data-lucide="menu" class="w-5 h-5 text-gray-600"></i>
                    </button>
                    <div>
                        <h2 class="text-lg font-semibold text-gray-800">لوحة التحكم</h2>
                        <div class="clock-container mt-1">
                            <div class="analog-clock">
                                <div class="hour-hand" id="hourHand"></div>
                                <div class="minute-hand" id="minuteHand"></div>
                                <div class="second-hand" id="secondHand"></div>
                                <div class="clock-center"></div>
                            </div>
                            <div class="date-time-info">
                                <div class="current-time" id="currentTime"></div>
                                <div class="current-date" id="currentDate"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="flex items-center space-x-4 space-x-reverse">
                    <div class="w-8 h-8 bg-gradient-to-r from-red-600 to-green-600 rounded-full flex items-center justify-center">
                        <span class="text-white text-sm font-bold">م</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- المحتوى -->
        <main class="p-6">
            <!-- صفحة الرئيسية -->
            <div class="page active" id="dashboard">
                <div class="mb-6">
                    <h1 class="text-2xl font-bold text-gray-800">لوحة التحكم</h1>
                    <p class="text-gray-600 mt-1">نظرة شاملة على أداء المؤسسة</p>
                </div>

                <!-- مؤشرات الأداء الرئيسية (KPIs) -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="kpi-card sales">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm opacity-90">إجمالي المبيعات</p>
                                <p class="text-2xl font-bold">0 ل.د</p>
                                <div class="trend-indicator trend-neutral mt-2">
                                    <i data-lucide="minus" class="w-3 h-3 ml-1"></i>
                                    لا توجد بيانات
                                </div>
                            </div>
                            <i data-lucide="trending-up" class="w-8 h-8 opacity-80"></i>
                        </div>
                    </div>
                    
                    <div class="kpi-card purchases">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm opacity-90">إجمالي المشتريات</p>
                                <p class="text-2xl font-bold">0 ل.د</p>
                                <div class="trend-indicator trend-neutral mt-2">
                                    <i data-lucide="minus" class="w-3 h-3 ml-1"></i>
                                    لا توجد بيانات
                                </div>
                            </div>
                            <i data-lucide="package" class="w-8 h-8 opacity-80"></i>
                        </div>
                    </div>
                    
                    <div class="kpi-card inventory">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm opacity-90">قيمة المخزون</p>
                                <p class="text-2xl font-bold">0 ل.د</p>
                                <div class="trend-indicator trend-neutral mt-2">
                                    <i data-lucide="minus" class="w-3 h-3 ml-1"></i>
                                    لا توجد بيانات
                                </div>
                            </div>
                            <i data-lucide="warehouse" class="w-8 h-8 opacity-80"></i>
                        </div>
                    </div>
                    
                    <div class="kpi-card profit">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm opacity-90">صافي الربح</p>
                                <p class="text-2xl font-bold">0 ل.د</p>
                                <div class="trend-indicator trend-neutral mt-2">
                                    <i data-lucide="minus" class="w-3 h-3 ml-1"></i>
                                    لا توجد بيانات
                                </div>
                            </div>
                            <i data-lucide="dollar-sign" class="w-8 h-8 opacity-80"></i>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات الموديولات -->
                <div class="module-stats">
                    <div class="module-stat">
                        <div class="module-stat-header">
                            <div class="module-stat-icon bg-blue-100">
                                <i data-lucide="shopping-cart" class="w-4 h-4 text-blue-600"></i>
                            </div>
                            <span class="module-stat-title">المبيعات</span>
                        </div>
                        <div class="module-stat-value">0</div>
                        <p class="text-xs text-gray-500 mt-1">فاتورة</p>
                    </div>
                    
                    <div class="module-stat">
                        <div class="module-stat-header">
                            <div class="module-stat-icon bg-green-100">
                                <i data-lucide="package" class="w-4 h-4 text-green-600"></i>
                            </div>
                            <span class="module-stat-title">المشتريات</span>
                        </div>
                        <div class="module-stat-value">0</div>
                        <p class="text-xs text-gray-500 mt-1">فاتورة</p>
                    </div>
                    
                    <div class="module-stat">
                        <div class="module-stat-header">
                            <div class="module-stat-icon bg-orange-100">
                                <i data-lucide="warehouse" class="w-4 h-4 text-orange-600"></i>
                            </div>
                            <span class="module-stat-title">الأصناف</span>
                        </div>
                        <div class="module-stat-value">0</div>
                        <p class="text-xs text-gray-500 mt-1">صنف</p>
                    </div>
                    
                    <div class="module-stat">
                        <div class="module-stat-header">
                            <div class="module-stat-icon bg-purple-100">
                                <i data-lucide="users" class="w-4 h-4 text-purple-600"></i>
                            </div>
                            <span class="module-stat-title">العملاء</span>
                        </div>
                        <div class="module-stat-value">0</div>
                        <p class="text-xs text-gray-500 mt-1">عميل</p>
                    </div>
                    
                    <div class="module-stat">
                        <div class="module-stat-header">
                            <div class="module-stat-icon bg-red-100">
                                <i data-lucide="truck" class="w-4 h-4 text-red-600"></i>
                            </div>
                            <span class="module-stat-title">الموردين</span>
                        </div>
                        <div class="module-stat-value">0</div>
                        <p class="text-xs text-gray-500 mt-1">مورد</p>
                    </div>
                    
                    <div class="module-stat">
                        <div class="module-stat-header">
                            <div class="module-stat-icon bg-indigo-100">
                                <i data-lucide="user-check" class="w-4 h-4 text-indigo-600"></i>
                            </div>
                            <span class="module-stat-title">الموظفين</span>
                        </div>
                        <div class="module-stat-value">0</div>
                        <p class="text-xs text-gray-500 mt-1">موظف</p>
                    </div>
                </div>

                <!-- الرسوم البيانية -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="text-lg font-semibold text-gray-800">المبيعات الشهرية</h3>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="salesChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="text-lg font-semibold text-gray-800">توزيع المبيعات</h3>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="salesDistributionChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="text-lg font-semibold text-gray-800">حركة المخزون</h3>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="inventoryChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="text-lg font-semibold text-gray-800">الأرباح والخسائر</h3>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="profitLossChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تحليلات متقدمة -->
                <div class="mt-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="text-lg font-semibold text-gray-800">تحليلات متقدمة</h3>
                        </div>
                        <div class="card-body">
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div class="text-center">
                                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                        <i data-lucide="trending-up" class="w-8 h-8 text-blue-600"></i>
                                    </div>
                                    <h4 class="font-semibold text-gray-800 mb-1">معدل النمو</h4>
                                    <p class="text-2xl font-bold text-blue-600">0%</p>
                                    <p class="text-sm text-gray-500">مقارنة بالشهر السابق</p>
                                </div>
                                
                                <div class="text-center">
                                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                        <i data-lucide="target" class="w-8 h-8 text-green-600"></i>
                                    </div>
                                    <h4 class="font-semibold text-gray-800 mb-1">تحقيق الأهداف</h4>
                                    <p class="text-2xl font-bold text-green-600">0%</p>
                                    <p class="text-sm text-gray-500">من المستهدف الشهري</p>
                                </div>
                                
                                <div class="text-center">
                                    <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                        <i data-lucide="activity" class="w-8 h-8 text-purple-600"></i>
                                    </div>
                                    <h4 class="font-semibold text-gray-800 mb-1">كفاءة العمليات</h4>
                                    <p class="text-2xl font-bold text-purple-600">0%</p>
                                    <p class="text-sm text-gray-500">معدل الإنجاز العام</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- صفحة فواتير المبيعات -->
            <div class="page" id="sales-invoices">
                <div class="mb-6">
                    <h1 class="text-2xl font-bold text-gray-800">فواتير المبيعات</h1>
                    <p class="text-gray-600 mt-1">إدارة فواتير المبيعات</p>
                </div>
                <div class="card">
                    <div class="card-body">
                        <p class="text-center text-gray-500">صفحة فواتير المبيعات - قيد التطوير</p>
                    </div>
                </div>
            </div>

            <!-- صفحة العملاء -->
            <div class="page" id="customers">
                <div class="mb-6">
                    <h1 class="text-2xl font-bold text-gray-800">العملاء</h1>
                    <p class="text-gray-600 mt-1">إدارة بيانات العملاء</p>
                </div>
                <div class="card">
                    <div class="card-body">
                        <p class="text-center text-gray-500">صفحة العملاء - قيد التطوير</p>
                    </div>
                </div>
            </div>

            <!-- صفحة فواتير المشتريات -->
            <div class="page" id="purchase-invoices">
                <div class="mb-6">
                    <h1 class="text-2xl font-bold text-gray-800">فواتير المشتريات</h1>
                    <p class="text-gray-600 mt-1">إدارة فواتير المشتريات</p>
                </div>
                <div class="card">
                    <div class="card-body">
                        <p class="text-center text-gray-500">صفحة فواتير المشتريات - قيد التطوير</p>
                    </div>
                </div>
            </div>

            <!-- صفحة الموردين -->
            <div class="page" id="suppliers">
                <div class="mb-6">
                    <h1 class="text-2xl font-bold text-gray-800">الموردين</h1>
                    <p class="text-gray-600 mt-1">إدارة بيانات الموردين</p>
                </div>
                <div class="card">
                    <div class="card-body">
                        <p class="text-center text-gray-500">صفحة الموردين - قيد التطوير</p>
                    </div>
                </div>
            </div>

            <!-- صفحة المخازن -->
            <div class="page" id="inventory">
                <div class="mb-6">
                    <h1 class="text-2xl font-bold text-gray-800">المخازن</h1>
                    <p class="text-gray-600 mt-1">نظرة عامة على المخزون</p>
                </div>
                <div class="card">
                    <div class="card-body">
                        <p class="text-center text-gray-500">صفحة المخازن - قيد التطوير</p>
                    </div>
                </div>
            </div>

            <!-- صفحة الأصناف -->
            <div class="page" id="items">
                <div class="mb-6">
                    <h1 class="text-2xl font-bold text-gray-800">الأصناف</h1>
                    <p class="text-gray-600 mt-1">إدارة الأصناف والمخزون</p>
                </div>
                <div class="card">
                    <div class="card-body">
                        <p class="text-center text-gray-500">صفحة الأصناف - قيد التطوير</p>
                    </div>
                </div>
            </div>

            <!-- صفحة دليل الحسابات -->
            <div class="page" id="accounts">
                <div class="mb-6">
                    <h1 class="text-2xl font-bold text-gray-800">دليل الحسابات</h1>
                    <p class="text-gray-600 mt-1">إدارة دليل الحسابات</p>
                </div>
                <div class="card">
                    <div class="card-body">
                        <p class="text-center text-gray-500">صفحة دليل الحسابات - قيد التطوير</p>
                    </div>
                </div>
            </div>

            <!-- صفحة دفتر اليومية -->
            <div class="page" id="journal">
                <div class="mb-6">
                    <h1 class="text-2xl font-bold text-gray-800">دفتر اليومية</h1>
                    <p class="text-gray-600 mt-1">إدارة القيود المحاسبية</p>
                </div>
                <div class="card">
                    <div class="card-body">
                        <p class="text-center text-gray-500">صفحة دفتر اليومية - قيد التطوير</p>
                    </div>
                </div>
            </div>

            <!-- صفحة الموظفين -->
            <div class="page" id="employees">
                <div class="mb-6">
                    <h1 class="text-2xl font-bold text-gray-800">الموظفين</h1>
                    <p class="text-gray-600 mt-1">إدارة بيانات الموظفين</p>
                </div>
                <div class="card">
                    <div class="card-body">
                        <p class="text-center text-gray-500">صفحة الموظفين - قيد التطوير</p>
                    </div>
                </div>
            </div>

            <!-- صفحة الرواتب -->
            <div class="page" id="payroll">
                <div class="mb-6">
                    <h1 class="text-2xl font-bold text-gray-800">الرواتب</h1>
                    <p class="text-gray-600 mt-1">إدارة الرواتب</p>
                </div>
                <div class="card">
                    <div class="card-body">
                        <p class="text-center text-gray-500">صفحة الرواتب - قيد التطوير</p>
                    </div>
                </div>
            </div>

            <!-- صفحة أوامر التصنيع -->
            <div class="page" id="manufacturing">
                <div class="mb-6">
                    <h1 class="text-2xl font-bold text-gray-800">أوامر التصنيع</h1>
                    <p class="text-gray-600 mt-1">إدارة أوامر التصنيع</p>
                </div>
                <div class="card">
                    <div class="card-body">
                        <p class="text-center text-gray-500">صفحة أوامر التصنيع - قيد التطوير</p>
                    </div>
                </div>
            </div>

            <!-- صفحة المصروفات -->
            <div class="page" id="expenses">
                <div class="mb-6">
                    <h1 class="text-2xl font-bold text-gray-800">المصروفات</h1>
                    <p class="text-gray-600 mt-1">إدارة المصروفات</p>
                </div>
                <div class="card">
                    <div class="card-body">
                        <p class="text-center text-gray-500">صفحة المصروفات - قيد التطوير</p>
                    </div>
                </div>
            </div>

            <!-- صفحة الإعدادات -->
            <div class="page" id="settings">
                <div class="mb-6">
                    <h1 class="text-2xl font-bold text-gray-800">الإعدادات</h1>
                    <p class="text-gray-600 mt-1">إعدادات النظام</p>
                </div>
                <div class="card">
                    <div class="card-body">
                        <p class="text-center text-gray-500">صفحة الإعدادات - قيد التطوير</p>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // تهيئة الأيقونات
        lucide.createIcons();
        
        // تفعيل طي وفتح الأقسام
        document.querySelectorAll('.nav-section-header').forEach(header => {
            header.addEventListener('click', function() {
                const section = this.getAttribute('data-section');
                const content = document.getElementById(section + '-content');
                
                // تبديل حالة القسم
                if (content.classList.contains('collapsed')) {
                    // فتح القسم
                    content.classList.remove('collapsed');
                    this.classList.add('expanded');
                } else {
                    // طي القسم
                    content.classList.add('collapsed');
                    this.classList.remove('expanded');
                }
            });
        });
        
        // التنقل بين الصفحات
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                
                // إزالة الفئة النشطة من جميع الروابط
                document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
                
                // إضافة الفئة النشطة للرابط المحدد
                this.classList.add('active');
                
                // إخفاء جميع الصفحات
                document.querySelectorAll('.page').forEach(page => page.classList.remove('active'));
                
                // إظهار الصفحة المحددة
                const pageId = this.getAttribute('data-page');
                const targetPage = document.getElementById(pageId);
                if (targetPage) {
                    targetPage.classList.add('active');
                } else {
                    // إذا لم تكن الصفحة موجودة، اعرض رسالة "قيد التطوير"
                    document.getElementById('dashboard').classList.add('active');
                    document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
                    document.querySelector('[data-page="dashboard"]').classList.add('active');
                }
            });
        });
        
        // تبديل الشريط الجانبي
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            
            sidebar.classList.toggle('closed');
            mainContent.classList.toggle('full');
        }
        
        // إخفاء الشريط الجانبي في الشاشات الصغيرة
        function checkScreenSize() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            
            if (window.innerWidth < 1024) {
                sidebar.classList.add('closed');
                mainContent.classList.add('full');
            } else {
                sidebar.classList.remove('closed');
                mainContent.classList.remove('full');
            }
        }
        
        // تشغيل عند تحميل الصفحة
        window.addEventListener('load', checkScreenSize);
        window.addEventListener('resize', checkScreenSize);
        
        // إنشاء الرسوم البيانية
        function createCharts() {
            // رسم بياني للمبيعات الشهرية
            const salesCtx = document.getElementById('salesChart');
            if (salesCtx) {
                new Chart(salesCtx, {
                    type: 'line',
                    data: {
                        labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                        datasets: [{
                            label: 'المبيعات (ل.د)',
                            data: [0, 0, 0, 0, 0, 0],
                            borderColor: '#3b82f6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            tension: 0.4,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    callback: function(value) {
                                        return value + ' ل.د';
                                    }
                                }
                            }
                        }
                    }
                });
            }
            
            // رسم بياني دائري لتوزيع المبيعات
            const distributionCtx = document.getElementById('salesDistributionChart');
            if (distributionCtx) {
                new Chart(distributionCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['لا توجد بيانات'],
                        datasets: [{
                            data: [1],
                            backgroundColor: ['#e5e7eb'],
                            borderWidth: 0
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }
            
            // رسم بياني للمخزون
            const inventoryCtx = document.getElementById('inventoryChart');
            if (inventoryCtx) {
                new Chart(inventoryCtx, {
                    type: 'bar',
                    data: {
                        labels: ['الأصناف المتوفرة', 'الأصناف النادرة', 'الأصناف المنتهية'],
                        datasets: [{
                            label: 'الكمية',
                            data: [0, 0, 0],
                            backgroundColor: [
                                'rgba(34, 197, 94, 0.8)',
                                'rgba(251, 191, 36, 0.8)',
                                'rgba(239, 68, 68, 0.8)'
                            ],
                            borderColor: [
                                'rgb(34, 197, 94)',
                                'rgb(251, 191, 36)',
                                'rgb(239, 68, 68)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }
            
            // رسم بياني للأرباح والخسائر
            const profitLossCtx = document.getElementById('profitLossChart');
            if (profitLossCtx) {
                new Chart(profitLossCtx, {
                    type: 'bar',
                    data: {
                        labels: ['الإيرادات', 'المصروفات', 'صافي الربح'],
                        datasets: [{
                            label: 'المبلغ (ل.د)',
                            data: [0, 0, 0],
                            backgroundColor: [
                                'rgba(34, 197, 94, 0.8)',
                                'rgba(239, 68, 68, 0.8)',
                                'rgba(59, 130, 246, 0.8)'
                            ],
                            borderColor: [
                                'rgb(34, 197, 94)',
                                'rgb(239, 68, 68)',
                                'rgb(59, 130, 246)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    callback: function(value) {
                                        return value + ' ل.د';
                                    }
                                }
                            }
                        }
                    }
                });
            }
        }
        
        // تشغيل الرسوم البيانية عند تحميل الصفحة
        window.addEventListener('load', createCharts);
        
        // تشغيل الساعة والتاريخ
        function updateClock() {
            const now = new Date();
            // الوقت بالإنجليزي
            const timeString = now.toLocaleTimeString('en-GB', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            });
            document.getElementById('currentTime').textContent = timeString;
            // التاريخ ميلادي
            const dateString = now.toLocaleDateString('en-GB', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
            document.getElementById('currentDate').textContent = dateString;
            // تحديث الساعة التناظرية
            const hours = now.getHours() % 12;
            const minutes = now.getMinutes();
            const seconds = now.getSeconds();
            const hourDegrees = (hours * 30) + (minutes * 0.5);
            const minuteDegrees = minutes * 6;
            const secondDegrees = seconds * 6;
            document.getElementById('hourHand').style.transform = `translate(-50%, -100%) rotate(${hourDegrees}deg)`;
            document.getElementById('minuteHand').style.transform = `translate(-50%, -100%) rotate(${minuteDegrees}deg)`;
            document.getElementById('secondHand').style.transform = `translate(-50%, -100%) rotate(${secondDegrees}deg)`;
        }
        
        // تحديث الساعة كل ثانية
        updateClock();
        setInterval(updateClock, 1000);
    </script>
</body>
</html> 